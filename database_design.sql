-- تصميم قاعدة البيانات لنظام إدارة المشاريع الهندسية - بلدية كفرنجة
-- Database Design for Engineering Project Management System - Kufranja Municipality

-- جدول الأقسام (Departments)
CREATE TABLE Departments (
    DepartmentID AUTOINCREMENT PRIMARY KEY,
    DepartmentName TEXT(100) NOT NULL,
    DepartmentHead TEXT(100),
    ContactInfo TEXT(255),
    CreatedDate DATETIME DEFAULT NOW(),
    IsActive YESNO DEFAULT TRUE
);

-- جدول الموظفين (Employees)
CREATE TABLE Employees (
    EmployeeID AUTOINCREMENT PRIMARY KEY,
    FullName TEXT(100) NOT NULL,
    DepartmentID LONG,
    Position TEXT(100),
    ContactInfo TEXT(255),
    Email TEXT(100),
    Phone TEXT(20),
    HireDate DATETIME,
    IsActive YESNO DEFAULT TRUE,
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (DepartmentID) REFERENCES Departments(DepartmentID)
);

-- جدول المستخدمين (Users)
CREATE TABLE Users (
    UserID AUTOINCREMENT PRIMARY KEY,
    Username TEXT(50) NOT NULL UNIQUE,
    Password TEXT(255) NOT NULL,
    EmployeeID LONG,
    UserRole TEXT(50) NOT NULL, -- Admin, ProjectManager, Employee, SystemManager
    IsActive YESNO DEFAULT TRUE,
    LastLogin DATETIME,
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (EmployeeID) REFERENCES Employees(EmployeeID)
);

-- جدول المقاولين (Contractors)
CREATE TABLE Contractors (
    ContractorID AUTOINCREMENT PRIMARY KEY,
    CompanyName TEXT(150) NOT NULL,
    ContactPerson TEXT(100),
    Phone TEXT(20),
    Email TEXT(100),
    Address TEXT(255),
    LicenseNumber TEXT(50),
    Rating SINGLE DEFAULT 0,
    IsActive YESNO DEFAULT TRUE,
    CreatedDate DATETIME DEFAULT NOW()
);

-- جدول المشاريع (Projects)
CREATE TABLE Projects (
    ProjectID AUTOINCREMENT PRIMARY KEY,
    ProjectName TEXT(200) NOT NULL,
    ProjectDescription MEMO,
    ProjectType TEXT(100), -- Infrastructure, Building, Road, etc.
    Location TEXT(255),
    StartDate DATETIME,
    EndDate DATETIME,
    PlannedBudget CURRENCY,
    ActualBudget CURRENCY DEFAULT 0,
    ProjectManager LONG,
    ContractorID LONG,
    Status TEXT(50) DEFAULT 'Planned', -- Planned, InProgress, OnHold, Completed, Cancelled
    Priority TEXT(20) DEFAULT 'Medium', -- Low, Medium, High, Critical
    CompletionPercentage SINGLE DEFAULT 0,
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT NOW(),
    LastModified DATETIME DEFAULT NOW(),
    FOREIGN KEY (ProjectManager) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (ContractorID) REFERENCES Contractors(ContractorID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول المهام (Tasks)
CREATE TABLE Tasks (
    TaskID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG NOT NULL,
    TaskName TEXT(200) NOT NULL,
    TaskDescription MEMO,
    AssignedTo LONG,
    StartDate DATETIME,
    EndDate DATETIME,
    PlannedDuration LONG, -- in days
    ActualDuration LONG, -- in days
    Status TEXT(50) DEFAULT 'NotStarted', -- NotStarted, InProgress, Completed, OnHold, Cancelled
    Priority TEXT(20) DEFAULT 'Medium',
    CompletionPercentage SINGLE DEFAULT 0,
    Dependencies TEXT(255), -- comma-separated TaskIDs
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT NOW(),
    LastModified DATETIME DEFAULT NOW(),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (AssignedTo) REFERENCES Employees(EmployeeID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول المصروفات (Expenses)
CREATE TABLE Expenses (
    ExpenseID AUTOINCREMENT PRIMARY KEY,
    ProjectID LONG NOT NULL,
    TaskID LONG,
    ExpenseType TEXT(100), -- Material, Labor, Equipment, Other
    Description TEXT(255),
    Amount CURRENCY NOT NULL,
    ExpenseDate DATETIME DEFAULT NOW(),
    ApprovedBy LONG,
    Status TEXT(50) DEFAULT 'Pending', -- Pending, Approved, Rejected
    ReceiptNumber TEXT(50),
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (TaskID) REFERENCES Tasks(TaskID),
    FOREIGN KEY (ApprovedBy) REFERENCES Users(UserID),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول التقارير (Reports)
CREATE TABLE Reports (
    ReportID AUTOINCREMENT PRIMARY KEY,
    ReportType TEXT(100) NOT NULL, -- Daily, Weekly, Monthly, ProjectSummary, Financial
    ReportTitle TEXT(200),
    ProjectID LONG,
    GeneratedBy LONG NOT NULL,
    GenerationDate DATETIME DEFAULT NOW(),
    ReportPeriodStart DATETIME,
    ReportPeriodEnd DATETIME,
    ReportContent MEMO,
    FilePath TEXT(255), -- path to generated report file
    Status TEXT(50) DEFAULT 'Generated', -- Generated, Reviewed, Approved
    FOREIGN KEY (ProjectID) REFERENCES Projects(ProjectID),
    FOREIGN KEY (GeneratedBy) REFERENCES Users(UserID)
);

-- جدول قوالب التقارير (Report Templates)
CREATE TABLE ReportTemplates (
    TemplateID AUTOINCREMENT PRIMARY KEY,
    TemplateName TEXT(100) NOT NULL,
    TemplateType TEXT(100), -- Daily, Weekly, Monthly, etc.
    TemplateDescription TEXT(255),
    TemplateFile TEXT(255), -- path to template file
    IsActive YESNO DEFAULT TRUE,
    CreatedBy LONG,
    CreatedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (CreatedBy) REFERENCES Users(UserID)
);

-- جدول المرفقات (Attachments)
CREATE TABLE Attachments (
    AttachmentID AUTOINCREMENT PRIMARY KEY,
    EntityType TEXT(50) NOT NULL, -- Project, Task, Report
    EntityID LONG NOT NULL,
    FileName TEXT(255) NOT NULL,
    FilePath TEXT(500) NOT NULL,
    FileSize LONG,
    FileType TEXT(50),
    UploadedBy LONG,
    UploadDate DATETIME DEFAULT NOW(),
    Description TEXT(255),
    FOREIGN KEY (UploadedBy) REFERENCES Users(UserID)
);

-- جدول سجل النشاطات (Activity Log)
CREATE TABLE ActivityLog (
    LogID AUTOINCREMENT PRIMARY KEY,
    UserID LONG NOT NULL,
    ActivityType TEXT(100) NOT NULL, -- Login, Create, Update, Delete, etc.
    EntityType TEXT(50), -- Project, Task, User, etc.
    EntityID LONG,
    Description TEXT(500),
    IPAddress TEXT(50),
    ActivityDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (UserID) REFERENCES Users(UserID)
);

-- جدول الإعدادات (Settings)
CREATE TABLE Settings (
    SettingID AUTOINCREMENT PRIMARY KEY,
    SettingKey TEXT(100) NOT NULL UNIQUE,
    SettingValue TEXT(500),
    SettingDescription TEXT(255),
    ModifiedBy LONG,
    ModifiedDate DATETIME DEFAULT NOW(),
    FOREIGN KEY (ModifiedBy) REFERENCES Users(UserID)
);

-- جدول الإشعارات (Notifications)
CREATE TABLE Notifications (
    NotificationID AUTOINCREMENT PRIMARY KEY,
    UserID LONG NOT NULL,
    Title TEXT(200) NOT NULL,
    Message TEXT(500),
    NotificationType TEXT(50), -- Info, Warning, Error, Success
    IsRead YESNO DEFAULT FALSE,
    CreatedDate DATETIME DEFAULT NOW(),
    ReadDate DATETIME,
    FOREIGN KEY (UserID) REFERENCES Users(UserID)
);
