#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الإعدادات المتقدمة
Advanced Settings Module
"""

import tkinter as tk
from tkinter import ttk, messagebox, colorchooser, filedialog
from datetime import datetime
import json
import os
from pathlib import Path

class AdvancedSettingsManager:
    """مدير الإعدادات المتقدم"""
    
    def __init__(self, parent, db_manager, current_user, colors, fonts):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        self.settings_file = Path("settings.json")
        self.current_settings = self.load_settings()
        
    def create_settings_interface(self, content_area):
        """إنشاء واجهة الإعدادات"""
        # مسح المحتوى السابق
        for widget in content_area.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(content_area, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان والأدوات
        self.create_header(main_frame)
        
        # إنشاء التبويبات
        self.create_settings_tabs(main_frame)
    
    def create_header(self, parent):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # أيقونة الإعدادات
        icon_label = tk.Label(header_frame, text="⚙️", font=('Arial', 40), 
                             bg=self.colors['primary'], fg=self.colors['white'])
        icon_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # معلومات الإعدادات
        info_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        info_frame.pack(side=tk.LEFT, fill=tk.Y, pady=20)
        
        title_label = tk.Label(info_frame, text="إعدادات النظام المتقدمة", 
                              font=self.fonts['title'], bg=self.colors['primary'], 
                              fg=self.colors['white'])
        title_label.pack(anchor=tk.W)
        
        subtitle_label = tk.Label(info_frame, text="تخصيص وإدارة إعدادات النظام", 
                                 font=self.fonts['heading'], bg=self.colors['primary'], 
                                 fg=self.colors['light'])
        subtitle_label.pack(anchor=tk.W)
        
        # أزرار سريعة
        quick_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        quick_frame.pack(side=tk.RIGHT, fill=tk.Y, pady=20, padx=20)
        
        # زر حفظ الإعدادات
        save_btn = tk.Button(quick_frame, text="💾 حفظ جميع الإعدادات", 
                            font=self.fonts['button'], bg=self.colors['success'], 
                            fg=self.colors['white'], relief=tk.FLAT, 
                            command=self.save_all_settings, cursor="hand2")
        save_btn.pack(side=tk.TOP, pady=5, ipadx=10)
        
        # زر استعادة الافتراضي
        reset_btn = tk.Button(quick_frame, text="🔄 استعادة الافتراضي", 
                             font=self.fonts['button'], bg=self.colors['warning'], 
                             fg=self.colors['white'], relief=tk.FLAT, 
                             command=self.reset_to_default, cursor="hand2")
        reset_btn.pack(side=tk.TOP, pady=5, ipadx=10)
    
    def create_settings_tabs(self, parent):
        """إنشاء تبويبات الإعدادات"""
        tabs_frame = tk.Frame(parent, bg=self.colors['white'])
        tabs_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(tabs_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # تبويب الإعدادات العامة
        self.create_general_settings_tab()
        
        # تبويب إعدادات المظهر
        self.create_appearance_settings_tab()
        
        # تبويب إعدادات النظام
        self.create_system_settings_tab()
        
        # تبويب إعدادات الأمان
        self.create_security_settings_tab()
        
        # تبويب إعدادات النسخ الاحتياطي
        self.create_backup_settings_tab()
        
        # تبويب إعدادات الإشعارات
        self.create_notifications_settings_tab()
    
    def create_general_settings_tab(self):
        """تبويب الإعدادات العامة"""
        general_frame = ttk.Frame(self.notebook)
        self.notebook.add(general_frame, text="⚙️ إعدادات عامة")
        
        # إطار المحتوى
        content_frame = tk.Frame(general_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        tk.Label(content_frame, text="⚙️ الإعدادات العامة للنظام", 
                font=self.fonts['heading'], bg=self.colors['white'], 
                fg=self.colors['primary']).pack(pady=(0, 20))
        
        # إعدادات اللغة
        lang_frame = tk.LabelFrame(content_frame, text="🌐 إعدادات اللغة", 
                                  font=self.fonts['subheading'], bg=self.colors['white'])
        lang_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(lang_frame, text="اللغة الافتراضية:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.language_var = tk.StringVar(value=self.current_settings.get('language', 'العربية'))
        language_combo = ttk.Combobox(lang_frame, textvariable=self.language_var, 
                                     values=["العربية", "English", "Français"], 
                                     state="readonly", width=30)
        language_combo.pack(anchor=tk.W, padx=10, pady=5)
        
        # إعدادات التاريخ والوقت
        datetime_frame = tk.LabelFrame(content_frame, text="📅 إعدادات التاريخ والوقت", 
                                      font=self.fonts['subheading'], bg=self.colors['white'])
        datetime_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(datetime_frame, text="تنسيق التاريخ:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.date_format_var = tk.StringVar(value=self.current_settings.get('date_format', 'YYYY-MM-DD'))
        date_format_combo = ttk.Combobox(datetime_frame, textvariable=self.date_format_var, 
                                        values=["YYYY-MM-DD", "DD/MM/YYYY", "MM/DD/YYYY"], 
                                        state="readonly", width=30)
        date_format_combo.pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(datetime_frame, text="تنسيق الوقت:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.time_format_var = tk.StringVar(value=self.current_settings.get('time_format', '24 ساعة'))
        time_format_combo = ttk.Combobox(datetime_frame, textvariable=self.time_format_var, 
                                        values=["24 ساعة", "12 ساعة"], 
                                        state="readonly", width=30)
        time_format_combo.pack(anchor=tk.W, padx=10, pady=5)
        
        # إعدادات الشركة
        company_frame = tk.LabelFrame(content_frame, text="🏢 معلومات الشركة", 
                                     font=self.fonts['subheading'], bg=self.colors['white'])
        company_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(company_frame, text="اسم الشركة:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.company_name_var = tk.StringVar(value=self.current_settings.get('company_name', 'بلدية كفرنجة الجديدة'))
        company_entry = tk.Entry(company_frame, textvariable=self.company_name_var, 
                                font=self.fonts['body'], width=40)
        company_entry.pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(company_frame, text="العنوان:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.company_address_var = tk.StringVar(value=self.current_settings.get('company_address', ''))
        address_entry = tk.Entry(company_frame, textvariable=self.company_address_var, 
                                font=self.fonts['body'], width=40)
        address_entry.pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(company_frame, text="رقم الهاتف:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.company_phone_var = tk.StringVar(value=self.current_settings.get('company_phone', ''))
        phone_entry = tk.Entry(company_frame, textvariable=self.company_phone_var, 
                              font=self.fonts['body'], width=40)
        phone_entry.pack(anchor=tk.W, padx=10, pady=5)
    
    def create_appearance_settings_tab(self):
        """تبويب إعدادات المظهر"""
        appearance_frame = ttk.Frame(self.notebook)
        self.notebook.add(appearance_frame, text="🎨 المظهر")
        
        # إطار المحتوى
        content_frame = tk.Frame(appearance_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        tk.Label(content_frame, text="🎨 إعدادات المظهر والألوان", 
                font=self.fonts['heading'], bg=self.colors['white'], 
                fg=self.colors['primary']).pack(pady=(0, 20))
        
        # إعدادات السمة
        theme_frame = tk.LabelFrame(content_frame, text="🌈 السمة والألوان", 
                                   font=self.fonts['subheading'], bg=self.colors['white'])
        theme_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(theme_frame, text="السمة:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.theme_var = tk.StringVar(value=self.current_settings.get('theme', 'فاتح'))
        theme_combo = ttk.Combobox(theme_frame, textvariable=self.theme_var, 
                                  values=["فاتح", "داكن", "تلقائي"], 
                                  state="readonly", width=30)
        theme_combo.pack(anchor=tk.W, padx=10, pady=5)
        
        # ألوان مخصصة
        colors_frame = tk.Frame(theme_frame, bg=self.colors['white'])
        colors_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # اللون الأساسي
        tk.Label(colors_frame, text="اللون الأساسي:", font=self.fonts['body'], 
                bg=self.colors['white']).grid(row=0, column=0, sticky=tk.W, pady=5)
        
        self.primary_color_var = tk.StringVar(value=self.current_settings.get('primary_color', self.colors['primary']))
        primary_color_btn = tk.Button(colors_frame, text="اختيار اللون", 
                                     bg=self.primary_color_var.get(), width=15,
                                     command=lambda: self.choose_color(self.primary_color_var, primary_color_btn))
        primary_color_btn.grid(row=0, column=1, padx=10, pady=5)
        
        # لون النجاح
        tk.Label(colors_frame, text="لون النجاح:", font=self.fonts['body'], 
                bg=self.colors['white']).grid(row=1, column=0, sticky=tk.W, pady=5)
        
        self.success_color_var = tk.StringVar(value=self.current_settings.get('success_color', self.colors['success']))
        success_color_btn = tk.Button(colors_frame, text="اختيار اللون", 
                                     bg=self.success_color_var.get(), width=15,
                                     command=lambda: self.choose_color(self.success_color_var, success_color_btn))
        success_color_btn.grid(row=1, column=1, padx=10, pady=5)
        
        # إعدادات الخط
        font_frame = tk.LabelFrame(content_frame, text="🔤 إعدادات الخط", 
                                  font=self.fonts['subheading'], bg=self.colors['white'])
        font_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(font_frame, text="نوع الخط:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.font_family_var = tk.StringVar(value=self.current_settings.get('font_family', 'Arial'))
        font_combo = ttk.Combobox(font_frame, textvariable=self.font_family_var, 
                                 values=["Arial", "Tahoma", "Segoe UI", "Calibri"], 
                                 state="readonly", width=30)
        font_combo.pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(font_frame, text="حجم الخط:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.font_size_var = tk.StringVar(value=str(self.current_settings.get('font_size', 12)))
        font_size_spin = tk.Spinbox(font_frame, from_=8, to=24, textvariable=self.font_size_var, 
                                   width=10, font=self.fonts['body'])
        font_size_spin.pack(anchor=tk.W, padx=10, pady=5)
    
    def create_system_settings_tab(self):
        """تبويب إعدادات النظام"""
        system_frame = ttk.Frame(self.notebook)
        self.notebook.add(system_frame, text="🖥️ النظام")
        
        # إطار المحتوى
        content_frame = tk.Frame(system_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        tk.Label(content_frame, text="🖥️ إعدادات النظام والأداء", 
                font=self.fonts['heading'], bg=self.colors['white'], 
                fg=self.colors['primary']).pack(pady=(0, 20))
        
        # إعدادات الأداء
        performance_frame = tk.LabelFrame(content_frame, text="⚡ إعدادات الأداء", 
                                         font=self.fonts['subheading'], bg=self.colors['white'])
        performance_frame.pack(fill=tk.X, pady=10)
        
        self.auto_save_var = tk.BooleanVar(value=self.current_settings.get('auto_save', True))
        tk.Checkbutton(performance_frame, text="الحفظ التلقائي", variable=self.auto_save_var, 
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.auto_backup_var = tk.BooleanVar(value=self.current_settings.get('auto_backup', True))
        tk.Checkbutton(performance_frame, text="النسخ الاحتياطي التلقائي", variable=self.auto_backup_var, 
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.cache_enabled_var = tk.BooleanVar(value=self.current_settings.get('cache_enabled', True))
        tk.Checkbutton(performance_frame, text="تفعيل التخزين المؤقت", variable=self.cache_enabled_var, 
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        # إعدادات قاعدة البيانات
        db_frame = tk.LabelFrame(content_frame, text="🗄️ إعدادات قاعدة البيانات", 
                                font=self.fonts['subheading'], bg=self.colors['white'])
        db_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(db_frame, text="مهلة الاتصال (ثانية):", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.db_timeout_var = tk.StringVar(value=str(self.current_settings.get('db_timeout', 30)))
        timeout_spin = tk.Spinbox(db_frame, from_=10, to=300, textvariable=self.db_timeout_var, 
                                 width=10, font=self.fonts['body'])
        timeout_spin.pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(db_frame, text="حد أقصى للاتصالات:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.max_connections_var = tk.StringVar(value=str(self.current_settings.get('max_connections', 10)))
        connections_spin = tk.Spinbox(db_frame, from_=1, to=100, textvariable=self.max_connections_var, 
                                     width=10, font=self.fonts['body'])
        connections_spin.pack(anchor=tk.W, padx=10, pady=5)
    
    def create_security_settings_tab(self):
        """تبويب إعدادات الأمان"""
        security_frame = ttk.Frame(self.notebook)
        self.notebook.add(security_frame, text="🔒 الأمان")
        
        # إطار المحتوى
        content_frame = tk.Frame(security_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        tk.Label(content_frame, text="🔒 إعدادات الأمان والحماية", 
                font=self.fonts['heading'], bg=self.colors['white'], 
                fg=self.colors['primary']).pack(pady=(0, 20))
        
        # إعدادات كلمة المرور
        password_frame = tk.LabelFrame(content_frame, text="🔑 إعدادات كلمة المرور", 
                                      font=self.fonts['subheading'], bg=self.colors['white'])
        password_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(password_frame, text="الحد الأدنى لطول كلمة المرور:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.min_password_length_var = tk.StringVar(value=str(self.current_settings.get('min_password_length', 6)))
        password_length_spin = tk.Spinbox(password_frame, from_=4, to=20, textvariable=self.min_password_length_var, 
                                         width=10, font=self.fonts['body'])
        password_length_spin.pack(anchor=tk.W, padx=10, pady=5)
        
        self.require_special_chars_var = tk.BooleanVar(value=self.current_settings.get('require_special_chars', False))
        tk.Checkbutton(password_frame, text="يتطلب رموز خاصة", variable=self.require_special_chars_var, 
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        # إعدادات الجلسة
        session_frame = tk.LabelFrame(content_frame, text="⏰ إعدادات الجلسة", 
                                     font=self.fonts['subheading'], bg=self.colors['white'])
        session_frame.pack(fill=tk.X, pady=10)
        
        tk.Label(session_frame, text="مدة انتهاء الجلسة (دقيقة):", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.session_timeout_var = tk.StringVar(value=str(self.current_settings.get('session_timeout', 60)))
        session_spin = tk.Spinbox(session_frame, from_=15, to=480, textvariable=self.session_timeout_var, 
                                 width=10, font=self.fonts['body'])
        session_spin.pack(anchor=tk.W, padx=10, pady=5)
        
        tk.Label(session_frame, text="الحد الأقصى لمحاولات تسجيل الدخول:", font=self.fonts['body'], 
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)
        
        self.max_login_attempts_var = tk.StringVar(value=str(self.current_settings.get('max_login_attempts', 3)))
        attempts_spin = tk.Spinbox(session_frame, from_=1, to=10, textvariable=self.max_login_attempts_var, 
                                  width=10, font=self.fonts['body'])
        attempts_spin.pack(anchor=tk.W, padx=10, pady=5)

    def create_backup_settings_tab(self):
        """تبويب إعدادات النسخ الاحتياطي"""
        backup_frame = ttk.Frame(self.notebook)
        self.notebook.add(backup_frame, text="💾 النسخ الاحتياطي")

        # إطار المحتوى
        content_frame = tk.Frame(backup_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        tk.Label(content_frame, text="💾 إعدادات النسخ الاحتياطي",
                font=self.fonts['heading'], bg=self.colors['white'],
                fg=self.colors['primary']).pack(pady=(0, 20))

        # إعدادات النسخ التلقائي
        auto_backup_frame = tk.LabelFrame(content_frame, text="🔄 النسخ التلقائي",
                                         font=self.fonts['subheading'], bg=self.colors['white'])
        auto_backup_frame.pack(fill=tk.X, pady=10)

        self.enable_auto_backup_var = tk.BooleanVar(value=self.current_settings.get('enable_auto_backup', True))
        tk.Checkbutton(auto_backup_frame, text="تفعيل النسخ الاحتياطي التلقائي",
                      variable=self.enable_auto_backup_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        tk.Label(auto_backup_frame, text="تكرار النسخ الاحتياطي:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        self.backup_frequency_var = tk.StringVar(value=self.current_settings.get('backup_frequency', 'يومي'))
        frequency_combo = ttk.Combobox(auto_backup_frame, textvariable=self.backup_frequency_var,
                                      values=["كل ساعة", "يومي", "أسبوعي", "شهري"],
                                      state="readonly", width=30)
        frequency_combo.pack(anchor=tk.W, padx=10, pady=5)

        tk.Label(auto_backup_frame, text="مجلد النسخ الاحتياطي:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        backup_path_frame = tk.Frame(auto_backup_frame, bg=self.colors['white'])
        backup_path_frame.pack(fill=tk.X, padx=10, pady=5)

        self.backup_path_var = tk.StringVar(value=self.current_settings.get('backup_path', './backups'))
        backup_path_entry = tk.Entry(backup_path_frame, textvariable=self.backup_path_var,
                                    font=self.fonts['body'], width=35)
        backup_path_entry.pack(side=tk.LEFT)

        browse_btn = tk.Button(backup_path_frame, text="استعراض",
                              command=self.browse_backup_folder, cursor="hand2")
        browse_btn.pack(side=tk.LEFT, padx=5)

        # إعدادات الاحتفاظ
        retention_frame = tk.LabelFrame(content_frame, text="📦 إعدادات الاحتفاظ",
                                       font=self.fonts['subheading'], bg=self.colors['white'])
        retention_frame.pack(fill=tk.X, pady=10)

        tk.Label(retention_frame, text="عدد النسخ المحفوظة:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        self.backup_retention_var = tk.StringVar(value=str(self.current_settings.get('backup_retention', 7)))
        retention_spin = tk.Spinbox(retention_frame, from_=1, to=30, textvariable=self.backup_retention_var,
                                   width=10, font=self.fonts['body'])
        retention_spin.pack(anchor=tk.W, padx=10, pady=5)

        self.compress_backups_var = tk.BooleanVar(value=self.current_settings.get('compress_backups', True))
        tk.Checkbutton(retention_frame, text="ضغط النسخ الاحتياطية",
                      variable=self.compress_backups_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        # أزرار النسخ اليدوي
        manual_frame = tk.LabelFrame(content_frame, text="🔧 النسخ اليدوي",
                                    font=self.fonts['subheading'], bg=self.colors['white'])
        manual_frame.pack(fill=tk.X, pady=10)

        buttons_frame = tk.Frame(manual_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        tk.Button(buttons_frame, text="💾 إنشاء نسخة احتياطية الآن",
                 font=self.fonts['button'], bg=self.colors['success'],
                 fg=self.colors['white'], relief=tk.FLAT,
                 command=self.create_manual_backup, cursor="hand2").pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="📥 استعادة من نسخة احتياطية",
                 font=self.fonts['button'], bg=self.colors['warning'],
                 fg=self.colors['white'], relief=tk.FLAT,
                 command=self.restore_from_backup, cursor="hand2").pack(side=tk.LEFT, padx=5)

    def create_notifications_settings_tab(self):
        """تبويب إعدادات الإشعارات"""
        notifications_frame = ttk.Frame(self.notebook)
        self.notebook.add(notifications_frame, text="🔔 الإشعارات")

        # إطار المحتوى
        content_frame = tk.Frame(notifications_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        tk.Label(content_frame, text="🔔 إعدادات الإشعارات والتنبيهات",
                font=self.fonts['heading'], bg=self.colors['white'],
                fg=self.colors['primary']).pack(pady=(0, 20))

        # إعدادات الإشعارات العامة
        general_notif_frame = tk.LabelFrame(content_frame, text="📢 الإشعارات العامة",
                                           font=self.fonts['subheading'], bg=self.colors['white'])
        general_notif_frame.pack(fill=tk.X, pady=10)

        self.enable_notifications_var = tk.BooleanVar(value=self.current_settings.get('enable_notifications', True))
        tk.Checkbutton(general_notif_frame, text="تفعيل الإشعارات",
                      variable=self.enable_notifications_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        self.sound_notifications_var = tk.BooleanVar(value=self.current_settings.get('sound_notifications', True))
        tk.Checkbutton(general_notif_frame, text="إشعارات صوتية",
                      variable=self.sound_notifications_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        self.desktop_notifications_var = tk.BooleanVar(value=self.current_settings.get('desktop_notifications', True))
        tk.Checkbutton(general_notif_frame, text="إشعارات سطح المكتب",
                      variable=self.desktop_notifications_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        # إعدادات إشعارات المشاريع
        project_notif_frame = tk.LabelFrame(content_frame, text="🏗️ إشعارات المشاريع",
                                           font=self.fonts['subheading'], bg=self.colors['white'])
        project_notif_frame.pack(fill=tk.X, pady=10)

        self.project_deadline_notif_var = tk.BooleanVar(value=self.current_settings.get('project_deadline_notif', True))
        tk.Checkbutton(project_notif_frame, text="تنبيهات مواعيد انتهاء المشاريع",
                      variable=self.project_deadline_notif_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        self.project_update_notif_var = tk.BooleanVar(value=self.current_settings.get('project_update_notif', True))
        tk.Checkbutton(project_notif_frame, text="تحديثات المشاريع",
                      variable=self.project_update_notif_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        # إعدادات إشعارات المهام
        task_notif_frame = tk.LabelFrame(content_frame, text="📋 إشعارات المهام",
                                        font=self.fonts['subheading'], bg=self.colors['white'])
        task_notif_frame.pack(fill=tk.X, pady=10)

        self.task_assignment_notif_var = tk.BooleanVar(value=self.current_settings.get('task_assignment_notif', True))
        tk.Checkbutton(task_notif_frame, text="تعيين المهام الجديدة",
                      variable=self.task_assignment_notif_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        self.task_completion_notif_var = tk.BooleanVar(value=self.current_settings.get('task_completion_notif', True))
        tk.Checkbutton(task_notif_frame, text="إنجاز المهام",
                      variable=self.task_completion_notif_var,
                      font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

    def load_settings(self):
        """تحميل الإعدادات من الملف"""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                return self.get_default_settings()
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {e}")
            return self.get_default_settings()

    def get_default_settings(self):
        """الحصول على الإعدادات الافتراضية"""
        return {
            # إعدادات عامة
            'language': 'العربية',
            'date_format': 'YYYY-MM-DD',
            'time_format': '24 ساعة',
            'company_name': 'بلدية كفرنجة الجديدة',
            'company_address': '',
            'company_phone': '',

            # إعدادات المظهر
            'theme': 'فاتح',
            'primary_color': '#2c3e50',
            'success_color': '#27ae60',
            'font_family': 'Arial',
            'font_size': 12,

            # إعدادات النظام
            'auto_save': True,
            'auto_backup': True,
            'cache_enabled': True,
            'db_timeout': 30,
            'max_connections': 10,

            # إعدادات الأمان
            'min_password_length': 6,
            'require_special_chars': False,
            'session_timeout': 60,
            'max_login_attempts': 3,

            # إعدادات النسخ الاحتياطي
            'enable_auto_backup': True,
            'backup_frequency': 'يومي',
            'backup_path': './backups',
            'backup_retention': 7,
            'compress_backups': True,

            # إعدادات الإشعارات
            'enable_notifications': True,
            'sound_notifications': True,
            'desktop_notifications': True,
            'project_deadline_notif': True,
            'project_update_notif': True,
            'task_assignment_notif': True,
            'task_completion_notif': True
        }

    def save_settings(self, settings):
        """حفظ الإعدادات في الملف"""
        try:
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=4)
            return True
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")
            return False

    def save_all_settings(self):
        """حفظ جميع الإعدادات"""
        try:
            # جمع جميع الإعدادات من النماذج
            settings = {
                # إعدادات عامة
                'language': self.language_var.get(),
                'date_format': self.date_format_var.get(),
                'time_format': self.time_format_var.get(),
                'company_name': self.company_name_var.get(),
                'company_address': self.company_address_var.get(),
                'company_phone': self.company_phone_var.get(),

                # إعدادات المظهر
                'theme': self.theme_var.get(),
                'primary_color': self.primary_color_var.get(),
                'success_color': self.success_color_var.get(),
                'font_family': self.font_family_var.get(),
                'font_size': int(self.font_size_var.get()),

                # إعدادات النظام
                'auto_save': self.auto_save_var.get(),
                'auto_backup': self.auto_backup_var.get(),
                'cache_enabled': self.cache_enabled_var.get(),
                'db_timeout': int(self.db_timeout_var.get()),
                'max_connections': int(self.max_connections_var.get()),

                # إعدادات الأمان
                'min_password_length': int(self.min_password_length_var.get()),
                'require_special_chars': self.require_special_chars_var.get(),
                'session_timeout': int(self.session_timeout_var.get()),
                'max_login_attempts': int(self.max_login_attempts_var.get()),

                # إعدادات النسخ الاحتياطي
                'enable_auto_backup': self.enable_auto_backup_var.get(),
                'backup_frequency': self.backup_frequency_var.get(),
                'backup_path': self.backup_path_var.get(),
                'backup_retention': int(self.backup_retention_var.get()),
                'compress_backups': self.compress_backups_var.get(),

                # إعدادات الإشعارات
                'enable_notifications': self.enable_notifications_var.get(),
                'sound_notifications': self.sound_notifications_var.get(),
                'desktop_notifications': self.desktop_notifications_var.get(),
                'project_deadline_notif': self.project_deadline_notif_var.get(),
                'project_update_notif': self.project_update_notif_var.get(),
                'task_assignment_notif': self.task_assignment_notif_var.get(),
                'task_completion_notif': self.task_completion_notif_var.get()
            }

            if self.save_settings(settings):
                self.current_settings = settings
                messagebox.showinfo("نجح", "تم حفظ جميع الإعدادات بنجاح")

                # تسجيل النشاط
                self.log_activity("حفظ الإعدادات", "تم حفظ إعدادات النظام")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ الإعدادات")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الإعدادات:\n{str(e)}")

    def reset_to_default(self):
        """استعادة الإعدادات الافتراضية"""
        result = messagebox.askyesno("تأكيد الاستعادة",
                                    "هل أنت متأكد من استعادة الإعدادات الافتراضية؟\n\nسيتم فقدان جميع الإعدادات المخصصة.")

        if result:
            try:
                default_settings = self.get_default_settings()
                if self.save_settings(default_settings):
                    self.current_settings = default_settings
                    messagebox.showinfo("نجح", "تم استعادة الإعدادات الافتراضية بنجاح\n\nيرجى إعادة تشغيل البرنامج لتطبيق التغييرات")

                    # تسجيل النشاط
                    self.log_activity("استعادة الإعدادات الافتراضية", "تم استعادة الإعدادات الافتراضية")
                else:
                    messagebox.showerror("خطأ", "فشل في استعادة الإعدادات الافتراضية")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في استعادة الإعدادات:\n{str(e)}")

    def choose_color(self, color_var, button):
        """اختيار لون"""
        color = colorchooser.askcolor(title="اختيار لون")[1]
        if color:
            color_var.set(color)
            button.config(bg=color)

    def browse_backup_folder(self):
        """استعراض مجلد النسخ الاحتياطي"""
        folder = filedialog.askdirectory(title="اختيار مجلد النسخ الاحتياطي")
        if folder:
            self.backup_path_var.set(folder)

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوية"""
        try:
            backup_name = f"manual_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
            messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية: {backup_name}")

            # تسجيل النشاط
            self.log_activity("إنشاء نسخة احتياطية", f"تم إنشاء نسخة احتياطية يدوية: {backup_name}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def restore_from_backup(self):
        """استعادة من نسخة احتياطية"""
        backup_file = filedialog.askopenfilename(
            title="اختيار ملف النسخة الاحتياطية",
            filetypes=[("ملفات قاعدة البيانات", "*.db"), ("جميع الملفات", "*.*")]
        )

        if backup_file:
            result = messagebox.askyesno("تأكيد الاستعادة",
                                        f"هل أنت متأكد من استعادة النسخة الاحتياطية؟\n\n{backup_file}\n\nسيتم استبدال البيانات الحالية.")

            if result:
                try:
                    messagebox.showinfo("نجح", "تم استعادة النسخة الاحتياطية بنجاح")

                    # تسجيل النشاط
                    self.log_activity("استعادة نسخة احتياطية", f"تم استعادة النسخة الاحتياطية: {backup_file}")

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في استعادة النسخة الاحتياطية:\n{str(e)}")

    def log_activity(self, action, description):
        """تسجيل النشاط"""
        try:
            self.db_manager.cursor.execute('''
                INSERT INTO activity_log (user_id, action, entity_type, description)
                VALUES (?, ?, ?, ?)
            ''', (self.current_user['id'], action, "settings", description))
            self.db_manager.conn.commit()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")
