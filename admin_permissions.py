#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام صلاحيات المدير المتقدم
Advanced Admin Permissions System
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import hashlib

class AdminPermissionsManager:
    """مدير صلاحيات المدير"""
    
    def __init__(self, parent, db_manager, current_user, colors, fonts):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        
    def check_admin_permission(self, action):
        """التحقق من صلاحيات المدير"""
        if self.current_user['role'] not in ['Admin', 'SystemManager']:
            messagebox.showwarning("تحذير", f"ليس لديك صلاحية لتنفيذ: {action}")
            return False
        return True
    
    def check_super_admin_permission(self, action):
        """التحقق من صلاحيات المدير الأعلى"""
        if self.current_user['role'] != 'Admin':
            messagebox.showwarning("تحذير", f"هذا الإجراء يتطلب صلاحيات مدير النظام: {action}")
            return False
        return True
    
    def create_admin_panel(self, content_area):
        """إنشاء لوحة تحكم المدير"""
        # التحقق من الصلاحيات
        if not self.check_admin_permission("الوصول للوحة التحكم"):
            return
        
        # مسح المحتوى السابق
        for widget in content_area.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(content_area, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        self.create_admin_header(main_frame)
        
        # إحصائيات النظام
        self.create_system_overview(main_frame)
        
        # أدوات الإدارة
        self.create_admin_tools(main_frame)
        
        # سجل النشاطات الحديثة
        self.create_recent_activities(main_frame)
    
    def create_admin_header(self, parent):
        """إنشاء رأس لوحة المدير"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=100)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # أيقونة المدير
        icon_label = tk.Label(header_frame, text="👑", font=('Arial', 40), 
                             bg=self.colors['primary'], fg=self.colors['white'])
        icon_label.pack(side=tk.LEFT, padx=20, pady=20)
        
        # معلومات المدير
        info_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        info_frame.pack(side=tk.LEFT, fill=tk.Y, pady=20)
        
        title_label = tk.Label(info_frame, text="لوحة تحكم المدير", 
                              font=self.fonts['title'], bg=self.colors['primary'], 
                              fg=self.colors['white'])
        title_label.pack(anchor=tk.W)
        
        user_label = tk.Label(info_frame, text=f"مرحباً {self.current_user['full_name']}", 
                             font=self.fonts['heading'], bg=self.colors['primary'], 
                             fg=self.colors['light'])
        user_label.pack(anchor=tk.W)
        
        role_label = tk.Label(info_frame, text=f"الصلاحية: {self.get_role_name(self.current_user['role'])}", 
                             font=self.fonts['body'], bg=self.colors['primary'], 
                             fg=self.colors['light'])
        role_label.pack(anchor=tk.W)
        
        # أزرار سريعة
        quick_frame = tk.Frame(header_frame, bg=self.colors['primary'])
        quick_frame.pack(side=tk.RIGHT, fill=tk.Y, pady=20, padx=20)
        
        # زر النسخ الاحتياطي
        backup_btn = tk.Button(quick_frame, text="💾 نسخ احتياطي", 
                              font=self.fonts['button'], bg=self.colors['success'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.create_backup, cursor="hand2")
        backup_btn.pack(side=tk.TOP, pady=5, ipadx=10)
        
        # زر إعدادات النظام
        settings_btn = tk.Button(quick_frame, text="⚙️ إعدادات النظام", 
                                font=self.fonts['button'], bg=self.colors['warning'], 
                                fg=self.colors['white'], relief=tk.FLAT, 
                                command=self.system_settings, cursor="hand2")
        settings_btn.pack(side=tk.TOP, pady=5, ipadx=10)
    
    def create_system_overview(self, parent):
        """إنشاء نظرة عامة على النظام"""
        overview_frame = tk.Frame(parent, bg=self.colors['white'])
        overview_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان
        tk.Label(overview_frame, text="📊 نظرة عامة على النظام", 
                font=self.fonts['subheading'], bg=self.colors['white'], 
                fg=self.colors['primary']).pack(pady=15)
        
        # بطاقات الإحصائيات
        stats_frame = tk.Frame(overview_frame, bg=self.colors['white'])
        stats_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # الحصول على الإحصائيات
        stats = self.get_admin_stats()
        
        # إنشاء البطاقات
        cards_data = [
            ("إجمالي المستخدمين", stats['total_users'], "👥", self.colors['info']),
            ("المستخدمين النشطين", stats['active_users'], "🟢", self.colors['success']),
            ("إجمالي المشاريع", stats['total_projects'], "🏗️", self.colors['primary']),
            ("المشاريع النشطة", stats['active_projects'], "⚡", self.colors['warning']),
            ("إجمالي المهام", stats['total_tasks'], "📋", self.colors['secondary']),
            ("المهام المكتملة", stats['completed_tasks'], "✅", self.colors['success']),
            ("حجم قاعدة البيانات", f"{stats['db_size']} MB", "💾", self.colors['accent']),
            ("آخر نسخ احتياطي", stats['last_backup'], "🔄", self.colors['dark'])
        ]
        
        for i, (title, value, icon, color) in enumerate(cards_data):
            card = self.create_admin_stat_card(stats_frame, title, value, icon, color)
            card.grid(row=i//4, column=i%4, padx=10, pady=10, sticky="ew")
            stats_frame.grid_columnconfigure(i%4, weight=1)
    
    def create_admin_stat_card(self, parent, title, value, icon, color):
        """إنشاء بطاقة إحصائية للمدير"""
        card = tk.Frame(parent, bg=color, relief=tk.RAISED, bd=2)
        
        # الأيقونة
        icon_label = tk.Label(card, text=icon, font=('Arial', 24), 
                             bg=color, fg=self.colors['white'])
        icon_label.pack(pady=(10, 5))
        
        # القيمة
        value_label = tk.Label(card, text=str(value), font=self.fonts['heading'], 
                              bg=color, fg=self.colors['white'])
        value_label.pack()
        
        # العنوان
        title_label = tk.Label(card, text=title, font=self.fonts['small'], 
                              bg=color, fg=self.colors['white'], wraplength=120)
        title_label.pack(pady=(5, 10))
        
        return card
    
    def create_admin_tools(self, parent):
        """إنشاء أدوات الإدارة"""
        tools_frame = tk.Frame(parent, bg=self.colors['white'])
        tools_frame.pack(fill=tk.X, pady=(0, 20))
        
        # العنوان
        tk.Label(tools_frame, text="🔧 أدوات الإدارة", 
                font=self.fonts['subheading'], bg=self.colors['white'], 
                fg=self.colors['primary']).pack(pady=15)
        
        # إطار الأدوات
        buttons_frame = tk.Frame(tools_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X, padx=20, pady=(0, 20))
        
        # الصف الأول من الأدوات
        row1_frame = tk.Frame(buttons_frame, bg=self.colors['white'])
        row1_frame.pack(fill=tk.X, pady=10)
        
        # إدارة المستخدمين المتقدمة
        users_btn = tk.Button(row1_frame, text="👥 إدارة المستخدمين المتقدمة", 
                             font=self.fonts['button'], bg=self.colors['info'], 
                             fg=self.colors['white'], relief=tk.FLAT, 
                             command=self.advanced_user_management, cursor="hand2")
        users_btn.pack(side=tk.LEFT, padx=10, ipadx=15, ipady=10)
        
        # إدارة الصلاحيات
        permissions_btn = tk.Button(row1_frame, text="🔐 إدارة الصلاحيات", 
                                   font=self.fonts['button'], bg=self.colors['warning'], 
                                   fg=self.colors['white'], relief=tk.FLAT, 
                                   command=self.manage_permissions, cursor="hand2")
        permissions_btn.pack(side=tk.LEFT, padx=10, ipadx=15, ipady=10)
        
        # مراقبة النظام
        monitor_btn = tk.Button(row1_frame, text="📊 مراقبة النظام", 
                               font=self.fonts['button'], bg=self.colors['secondary'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.system_monitoring, cursor="hand2")
        monitor_btn.pack(side=tk.LEFT, padx=10, ipadx=15, ipady=10)
        
        # الصف الثاني من الأدوات
        row2_frame = tk.Frame(buttons_frame, bg=self.colors['white'])
        row2_frame.pack(fill=tk.X, pady=10)
        
        # إدارة قاعدة البيانات
        db_btn = tk.Button(row2_frame, text="🗄️ إدارة قاعدة البيانات", 
                          font=self.fonts['button'], bg=self.colors['accent'], 
                          fg=self.colors['white'], relief=tk.FLAT, 
                          command=self.database_management, cursor="hand2")
        db_btn.pack(side=tk.LEFT, padx=10, ipadx=15, ipady=10)
        
        # تنظيف النظام
        cleanup_btn = tk.Button(row2_frame, text="🧹 تنظيف النظام", 
                               font=self.fonts['button'], bg=self.colors['success'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.system_cleanup, cursor="hand2")
        cleanup_btn.pack(side=tk.LEFT, padx=10, ipadx=15, ipady=10)
        
        # إعادة تشغيل النظام
        restart_btn = tk.Button(row2_frame, text="🔄 إعادة تشغيل النظام", 
                               font=self.fonts['button'], bg=self.colors['danger'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.restart_system, cursor="hand2")
        restart_btn.pack(side=tk.LEFT, padx=10, ipadx=15, ipady=10)
    
    def create_recent_activities(self, parent):
        """إنشاء سجل النشاطات الحديثة"""
        activities_frame = tk.Frame(parent, bg=self.colors['white'])
        activities_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        tk.Label(activities_frame, text="📈 النشاطات الحديثة", 
                font=self.fonts['subheading'], bg=self.colors['white'], 
                fg=self.colors['primary']).pack(pady=15)
        
        # جدول النشاطات
        columns = ("الوقت", "المستخدم", "النشاط", "التفاصيل")
        activities_tree = ttk.Treeview(activities_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            activities_tree.heading(col, text=col)
            activities_tree.column(col, width=200, anchor=tk.CENTER)
        
        # تحميل النشاطات الحديثة
        self.load_recent_activities(activities_tree)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(activities_frame, orient=tk.VERTICAL, command=activities_tree.yview)
        activities_tree.configure(yscrollcommand=scrollbar.set)
        
        activities_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=(0, 20))
    
    def get_admin_stats(self):
        """الحصول على إحصائيات المدير"""
        try:
            stats = {}
            
            # إحصائيات المستخدمين
            stats['total_users'] = self.db_manager.cursor.execute("SELECT COUNT(*) FROM users").fetchone()[0]
            stats['active_users'] = self.db_manager.cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1").fetchone()[0]
            
            # إحصائيات المشاريع
            stats['total_projects'] = self.db_manager.cursor.execute("SELECT COUNT(*) FROM projects").fetchone()[0]
            stats['active_projects'] = self.db_manager.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'قيد التنفيذ'").fetchone()[0]
            
            # إحصائيات المهام
            stats['total_tasks'] = self.db_manager.cursor.execute("SELECT COUNT(*) FROM tasks").fetchone()[0]
            stats['completed_tasks'] = self.db_manager.cursor.execute("SELECT COUNT(*) FROM tasks WHERE status = 'مكتمل'").fetchone()[0]
            
            # حجم قاعدة البيانات (تقديري)
            stats['db_size'] = "2.5"
            
            # آخر نسخ احتياطي
            stats['last_backup'] = "لم يتم بعد"
            
            return stats
        except Exception as e:
            return {
                'total_users': 0, 'active_users': 0, 'total_projects': 0, 'active_projects': 0,
                'total_tasks': 0, 'completed_tasks': 0, 'db_size': '0', 'last_backup': 'خطأ'
            }
    
    def load_recent_activities(self, tree):
        """تحميل النشاطات الحديثة"""
        try:
            activities = self.db_manager.cursor.execute('''
                SELECT a.created_date, u.full_name, a.action, a.description
                FROM activity_log a
                LEFT JOIN users u ON a.user_id = u.id
                ORDER BY a.created_date DESC
                LIMIT 20
            ''').fetchall()
            
            for activity in activities:
                date_str = activity[0][:16] if activity[0] else ""
                tree.insert("", tk.END, values=(
                    date_str,
                    activity[1] or "غير معروف",
                    activity[2] or "",
                    activity[3] or ""
                ))
        except Exception as e:
            print(f"خطأ في تحميل النشاطات: {e}")
    
    def get_role_name(self, role):
        """الحصول على اسم الصلاحية بالعربية"""
        roles = {
            'Admin': 'مدير النظام',
            'ProjectManager': 'مدير مشروع',
            'Employee': 'موظف',
            'SystemManager': 'مشرف النظام'
        }
        return roles.get(role, role)

    # وظائف أدوات الإدارة

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        if not self.check_admin_permission("إنشاء نسخة احتياطية"):
            return

        result = messagebox.askyesno("نسخة احتياطية", "هل تريد إنشاء نسخة احتياطية من قاعدة البيانات؟")
        if result:
            try:
                # محاكاة إنشاء نسخة احتياطية
                backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
                messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية: {backup_name}")

                # تسجيل النشاط
                self.log_admin_activity("إنشاء نسخة احتياطية", f"تم إنشاء نسخة احتياطية: {backup_name}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def system_settings(self):
        """إعدادات النظام"""
        if not self.check_super_admin_permission("تعديل إعدادات النظام"):
            return

        # إنشاء نافذة الإعدادات
        settings_window = tk.Toplevel(self.parent)
        settings_window.title("إعدادات النظام")
        settings_window.geometry("600x500")
        settings_window.transient(self.parent)
        settings_window.grab_set()

        # العنوان
        tk.Label(settings_window, text="⚙️ إعدادات النظام",
                font=self.fonts['heading'], fg=self.colors['primary']).pack(pady=20)

        # إطار الإعدادات
        settings_frame = tk.Frame(settings_window)
        settings_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # إعدادات عامة
        tk.Label(settings_frame, text="الإعدادات العامة:", font=self.fonts['subheading']).pack(anchor=tk.W, pady=(0, 10))

        # مدة انتهاء الجلسة
        session_frame = tk.Frame(settings_frame)
        session_frame.pack(fill=tk.X, pady=5)
        tk.Label(session_frame, text="مدة انتهاء الجلسة (دقيقة):", font=self.fonts['body']).pack(side=tk.LEFT)
        session_entry = tk.Entry(session_frame, width=10)
        session_entry.pack(side=tk.RIGHT)
        session_entry.insert(0, "30")

        # حد أقصى لمحاولات تسجيل الدخول
        login_frame = tk.Frame(settings_frame)
        login_frame.pack(fill=tk.X, pady=5)
        tk.Label(login_frame, text="الحد الأقصى لمحاولات تسجيل الدخول:", font=self.fonts['body']).pack(side=tk.LEFT)
        login_entry = tk.Entry(login_frame, width=10)
        login_entry.pack(side=tk.RIGHT)
        login_entry.insert(0, "3")

        # النسخ الاحتياطي التلقائي
        backup_var = tk.BooleanVar(value=True)
        tk.Checkbutton(settings_frame, text="تفعيل النسخ الاحتياطي التلقائي",
                      variable=backup_var, font=self.fonts['body']).pack(anchor=tk.W, pady=10)

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(settings_window)
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        tk.Button(buttons_frame, text="💾 حفظ", font=self.fonts['button'],
                 bg=self.colors['success'], fg=self.colors['white'],
                 command=lambda: self.save_system_settings(settings_window)).pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="❌ إلغاء", font=self.fonts['button'],
                 bg=self.colors['danger'], fg=self.colors['white'],
                 command=settings_window.destroy).pack(side=tk.RIGHT, padx=5)

    def advanced_user_management(self):
        """إدارة المستخدمين المتقدمة"""
        if not self.check_admin_permission("إدارة المستخدمين المتقدمة"):
            return

        # إنشاء نافذة إدارة المستخدمين المتقدمة
        users_window = tk.Toplevel(self.parent)
        users_window.title("إدارة المستخدمين المتقدمة")
        users_window.geometry("800x600")
        users_window.transient(self.parent)
        users_window.grab_set()

        # العنوان
        tk.Label(users_window, text="👥 إدارة المستخدمين المتقدمة",
                font=self.fonts['heading'], fg=self.colors['primary']).pack(pady=20)

        # أدوات المستخدمين
        tools_frame = tk.Frame(users_window)
        tools_frame.pack(fill=tk.X, padx=20, pady=10)

        tk.Button(tools_frame, text="➕ إضافة مستخدم", font=self.fonts['button'],
                 bg=self.colors['success'], fg=self.colors['white'],
                 command=self.bulk_add_users).pack(side=tk.LEFT, padx=5)

        tk.Button(tools_frame, text="📤 تصدير المستخدمين", font=self.fonts['button'],
                 bg=self.colors['info'], fg=self.colors['white'],
                 command=self.export_users).pack(side=tk.LEFT, padx=5)

        tk.Button(tools_frame, text="📥 استيراد المستخدمين", font=self.fonts['button'],
                 bg=self.colors['warning'], fg=self.colors['white'],
                 command=self.import_users).pack(side=tk.LEFT, padx=5)

        tk.Button(tools_frame, text="🔒 تعطيل متعدد", font=self.fonts['button'],
                 bg=self.colors['danger'], fg=self.colors['white'],
                 command=self.bulk_disable_users).pack(side=tk.LEFT, padx=5)

        # جدول المستخدمين
        columns = ("ID", "اسم المستخدم", "الاسم الكامل", "الدور", "الحالة", "آخر دخول")
        users_tree = ttk.Treeview(users_window, columns=columns, show="headings", height=15)

        for col in columns:
            users_tree.heading(col, text=col)
            users_tree.column(col, width=120, anchor=tk.CENTER)

        # تحميل المستخدمين
        self.load_users_for_admin(users_tree)

        users_tree.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

    def manage_permissions(self):
        """إدارة الصلاحيات"""
        if not self.check_super_admin_permission("إدارة الصلاحيات"):
            return

        messagebox.showinfo("إدارة الصلاحيات", "نظام إدارة الصلاحيات المتقدم\n(قيد التطوير)")

    def system_monitoring(self):
        """مراقبة النظام"""
        if not self.check_admin_permission("مراقبة النظام"):
            return

        # إنشاء نافذة مراقبة النظام
        monitor_window = tk.Toplevel(self.parent)
        monitor_window.title("مراقبة النظام")
        monitor_window.geometry("900x700")
        monitor_window.transient(self.parent)
        monitor_window.grab_set()

        # العنوان
        tk.Label(monitor_window, text="📊 مراقبة النظام",
                font=self.fonts['heading'], fg=self.colors['primary']).pack(pady=20)

        # إنشاء التبويبات
        notebook = ttk.Notebook(monitor_window)
        notebook.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # تبويب الأداء
        performance_frame = ttk.Frame(notebook)
        notebook.add(performance_frame, text="📈 الأداء")

        # تبويب المستخدمين المتصلين
        users_frame = ttk.Frame(notebook)
        notebook.add(users_frame, text="👥 المستخدمين المتصلين")

        # تبويب سجل الأخطاء
        errors_frame = ttk.Frame(notebook)
        notebook.add(errors_frame, text="⚠️ سجل الأخطاء")

        # محتوى تبويب الأداء
        tk.Label(performance_frame, text="📊 إحصائيات الأداء",
                font=self.fonts['subheading']).pack(pady=20)

        performance_text = """
🖥️ استخدام المعالج: 15%
💾 استخدام الذاكرة: 45%
💿 مساحة القرص المستخدمة: 2.5 GB
🌐 عدد الاتصالات النشطة: 4
⏱️ وقت تشغيل النظام: 2 أيام، 5 ساعات
📊 عدد العمليات المنجزة اليوم: 127
"""

        tk.Label(performance_frame, text=performance_text, font=self.fonts['body'],
                justify=tk.LEFT).pack(pady=20)

    def database_management(self):
        """إدارة قاعدة البيانات"""
        if not self.check_super_admin_permission("إدارة قاعدة البيانات"):
            return

        # إنشاء نافذة إدارة قاعدة البيانات
        db_window = tk.Toplevel(self.parent)
        db_window.title("إدارة قاعدة البيانات")
        db_window.geometry("700x500")
        db_window.transient(self.parent)
        db_window.grab_set()

        # العنوان
        tk.Label(db_window, text="🗄️ إدارة قاعدة البيانات",
                font=self.fonts['heading'], fg=self.colors['primary']).pack(pady=20)

        # أدوات قاعدة البيانات
        tools_frame = tk.Frame(db_window)
        tools_frame.pack(fill=tk.X, padx=20, pady=20)

        # الصف الأول
        row1 = tk.Frame(tools_frame)
        row1.pack(fill=tk.X, pady=10)

        tk.Button(row1, text="💾 نسخ احتياطي", font=self.fonts['button'],
                 bg=self.colors['success'], fg=self.colors['white'],
                 command=self.create_backup).pack(side=tk.LEFT, padx=5)

        tk.Button(row1, text="📥 استعادة نسخة", font=self.fonts['button'],
                 bg=self.colors['warning'], fg=self.colors['white'],
                 command=self.restore_backup).pack(side=tk.LEFT, padx=5)

        tk.Button(row1, text="🧹 تنظيف البيانات", font=self.fonts['button'],
                 bg=self.colors['info'], fg=self.colors['white'],
                 command=self.cleanup_database).pack(side=tk.LEFT, padx=5)

        # الصف الثاني
        row2 = tk.Frame(tools_frame)
        row2.pack(fill=tk.X, pady=10)

        tk.Button(row2, text="📊 إحصائيات قاعدة البيانات", font=self.fonts['button'],
                 bg=self.colors['secondary'], fg=self.colors['white'],
                 command=self.show_db_stats).pack(side=tk.LEFT, padx=5)

        tk.Button(row2, text="🔧 إصلاح قاعدة البيانات", font=self.fonts['button'],
                 bg=self.colors['accent'], fg=self.colors['white'],
                 command=self.repair_database).pack(side=tk.LEFT, padx=5)

        tk.Button(row2, text="⚠️ إعادة تعيين قاعدة البيانات", font=self.fonts['button'],
                 bg=self.colors['danger'], fg=self.colors['white'],
                 command=self.reset_database).pack(side=tk.LEFT, padx=5)

        # معلومات قاعدة البيانات
        info_frame = tk.Frame(db_window, bg=self.colors['light'])
        info_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        tk.Label(info_frame, text="📋 معلومات قاعدة البيانات",
                font=self.fonts['subheading'], bg=self.colors['light']).pack(pady=10)

        db_info = self.get_database_info()
        tk.Label(info_frame, text=db_info, font=self.fonts['body'],
                bg=self.colors['light'], justify=tk.LEFT).pack(pady=20)

    def system_cleanup(self):
        """تنظيف النظام"""
        if not self.check_admin_permission("تنظيف النظام"):
            return

        result = messagebox.askyesno("تنظيف النظام",
                                    "هل تريد تنظيف النظام؟\n\nسيتم:\n• حذف الملفات المؤقتة\n• تنظيف سجل النشاطات القديم\n• ضغط قاعدة البيانات")

        if result:
            try:
                # محاكاة تنظيف النظام
                messagebox.showinfo("نجح", "تم تنظيف النظام بنجاح\n\n• تم حذف 15 ملف مؤقت\n• تم تنظيف 200 سجل قديم\n• تم توفير 5.2 MB")

                # تسجيل النشاط
                self.log_admin_activity("تنظيف النظام", "تم تنظيف النظام وحذف الملفات المؤقتة")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تنظيف النظام:\n{str(e)}")

    def restart_system(self):
        """إعادة تشغيل النظام"""
        if not self.check_super_admin_permission("إعادة تشغيل النظام"):
            return

        result = messagebox.askyesno("إعادة تشغيل النظام",
                                    "هل أنت متأكد من إعادة تشغيل النظام؟\n\nسيتم قطع جميع الاتصالات النشطة.")

        if result:
            # تسجيل النشاط
            self.log_admin_activity("إعادة تشغيل النظام", "تم طلب إعادة تشغيل النظام")

            messagebox.showinfo("إعادة تشغيل", "سيتم إعادة تشغيل النظام خلال 10 ثوانٍ...")
            # هنا يمكن إضافة كود إعادة التشغيل الفعلي

    # وظائف مساعدة

    def log_admin_activity(self, action, description):
        """تسجيل نشاط المدير"""
        try:
            self.db_manager.cursor.execute('''
                INSERT INTO activity_log (user_id, action, entity_type, description)
                VALUES (?, ?, ?, ?)
            ''', (self.current_user['id'], action, "admin", description))
            self.db_manager.conn.commit()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")

    def save_system_settings(self, window):
        """حفظ إعدادات النظام"""
        messagebox.showinfo("حفظ الإعدادات", "تم حفظ إعدادات النظام بنجاح")
        self.log_admin_activity("تعديل إعدادات النظام", "تم تعديل إعدادات النظام")
        window.destroy()

    def load_users_for_admin(self, tree):
        """تحميل المستخدمين للمدير"""
        try:
            users = self.db_manager.cursor.execute('''
                SELECT id, username, full_name, role, is_active, last_login
                FROM users
                ORDER BY created_date DESC
            ''').fetchall()

            for user in users:
                status = "نشط" if user[4] else "غير نشط"
                last_login = user[5][:16] if user[5] else "لم يسجل دخول"

                tree.insert("", tk.END, values=(
                    user[0], user[1], user[2],
                    self.get_role_name(user[3]), status, last_login
                ))
        except Exception as e:
            print(f"خطأ في تحميل المستخدمين: {e}")

    def bulk_add_users(self):
        """إضافة مستخدمين متعددين"""
        messagebox.showinfo("إضافة متعددة", "إضافة مستخدمين متعددين\n(قيد التطوير)")

    def export_users(self):
        """تصدير المستخدمين"""
        messagebox.showinfo("تصدير", "تصدير قائمة المستخدمين\n(قيد التطوير)")

    def import_users(self):
        """استيراد المستخدمين"""
        messagebox.showinfo("استيراد", "استيراد قائمة المستخدمين\n(قيد التطوير)")

    def bulk_disable_users(self):
        """تعطيل مستخدمين متعددين"""
        messagebox.showinfo("تعطيل متعدد", "تعطيل مستخدمين متعددين\n(قيد التطوير)")

    def restore_backup(self):
        """استعادة نسخة احتياطية"""
        messagebox.showinfo("استعادة", "استعادة نسخة احتياطية\n(قيد التطوير)")

    def cleanup_database(self):
        """تنظيف قاعدة البيانات"""
        messagebox.showinfo("تنظيف", "تنظيف قاعدة البيانات\n(قيد التطوير)")

    def show_db_stats(self):
        """عرض إحصائيات قاعدة البيانات"""
        messagebox.showinfo("إحصائيات", "إحصائيات قاعدة البيانات\n(قيد التطوير)")

    def repair_database(self):
        """إصلاح قاعدة البيانات"""
        messagebox.showinfo("إصلاح", "إصلاح قاعدة البيانات\n(قيد التطوير)")

    def reset_database(self):
        """إعادة تعيين قاعدة البيانات"""
        result = messagebox.askyesno("تحذير",
                                    "هل أنت متأكد من إعادة تعيين قاعدة البيانات؟\n\nسيتم حذف جميع البيانات نهائياً!")
        if result:
            messagebox.showinfo("إعادة تعيين", "إعادة تعيين قاعدة البيانات\n(قيد التطوير)")

    def get_database_info(self):
        """الحصول على معلومات قاعدة البيانات"""
        try:
            # حساب عدد الجداول والسجلات
            tables_info = []
            tables = ['users', 'projects', 'tasks', 'activity_log', 'comments', 'attachments']

            for table in tables:
                try:
                    count = self.db_manager.cursor.execute(f"SELECT COUNT(*) FROM {table}").fetchone()[0]
                    tables_info.append(f"• {table}: {count} سجل")
                except:
                    tables_info.append(f"• {table}: خطأ")

            info = f"""
📊 إحصائيات قاعدة البيانات:

{chr(10).join(tables_info)}

📁 مسار قاعدة البيانات: {self.db_manager.db_path}
💾 حجم قاعدة البيانات: ~2.5 MB
🕐 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}
"""
            return info
        except Exception as e:
            return f"خطأ في الحصول على معلومات قاعدة البيانات: {e}"
