#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المستخدمين المتقدمة
Advanced Users Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import hashlib
import os
from pathlib import Path

class AdvancedUsersManager:
    """مدير المستخدمين المتقدم"""
    
    def __init__(self, parent, db_manager, current_user, colors, fonts):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        self.users_tree = None
        self.search_var = tk.StringVar()
        self.filter_role_var = tk.StringVar(value="جميع الأدوار")
        self.filter_department_var = tk.StringVar(value="جميع الأقسام")
        self.filter_status_var = tk.StringVar(value="جميع الحالات")
        
    def create_users_interface(self, content_area):
        """إنشاء واجهة إدارة المستخدمين"""
        # مسح المحتوى السابق
        for widget in content_area.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(content_area, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان والأدوات
        self.create_header(main_frame)
        
        # شريط البحث والفلاتر
        self.create_filters_bar(main_frame)
        
        # إحصائيات المستخدمين
        self.create_stats_bar(main_frame)
        
        # جدول المستخدمين
        self.create_users_table(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
        
        # تحميل البيانات
        self.load_users()
    
    def create_header(self, parent):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(parent, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(header_frame, text="👥 إدارة المستخدمين", 
                              font=self.fonts['title'], bg=self.colors['white'], 
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)
        
        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)
        
        # زر مستخدم جديد
        new_btn = tk.Button(tools_frame, text="➕ مستخدم جديد", 
                           font=self.fonts['button'], bg=self.colors['success'], 
                           fg=self.colors['white'], relief=tk.FLAT, 
                           command=self.add_user, cursor="hand2")
        new_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تصدير
        export_btn = tk.Button(tools_frame, text="📤 تصدير", 
                              font=self.fonts['button'], bg=self.colors['info'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.export_users, cursor="hand2")
        export_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر استيراد
        import_btn = tk.Button(tools_frame, text="📥 استيراد", 
                              font=self.fonts['button'], bg=self.colors['warning'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.import_users, cursor="hand2")
        import_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تحديث
        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث", 
                               font=self.fonts['button'], bg=self.colors['secondary'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.load_users, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
    
    def create_filters_bar(self, parent):
        """إنشاء شريط الفلاتر"""
        filters_frame = tk.Frame(parent, bg=self.colors['white'], height=60)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        filters_frame.pack_propagate(False)
        
        # البحث
        search_label = tk.Label(filters_frame, text="🔍 البحث:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        search_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        search_entry = tk.Entry(filters_frame, textvariable=self.search_var, 
                               font=self.fonts['body'], width=25, 
                               bg=self.colors['light'])
        search_entry.pack(side=tk.LEFT, padx=5, pady=15, ipady=5)
        search_entry.bind('<KeyRelease>', self.search_users)
        
        # فلتر الدور
        role_label = tk.Label(filters_frame, text="👤 الدور:", 
                             font=self.fonts['body'], bg=self.colors['white'])
        role_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        roles = self.get_roles_list()
        role_combo = ttk.Combobox(filters_frame, textvariable=self.filter_role_var, 
                                 values=roles, state="readonly", width=15)
        role_combo.pack(side=tk.LEFT, padx=5, pady=15)
        role_combo.bind('<<ComboboxSelected>>', self.filter_users)
        
        # فلتر القسم
        dept_label = tk.Label(filters_frame, text="🏢 القسم:", 
                             font=self.fonts['body'], bg=self.colors['white'])
        dept_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        departments = self.get_departments_list()
        dept_combo = ttk.Combobox(filters_frame, textvariable=self.filter_department_var, 
                                 values=departments, state="readonly", width=15)
        dept_combo.pack(side=tk.LEFT, padx=5, pady=15)
        dept_combo.bind('<<ComboboxSelected>>', self.filter_users)
        
        # فلتر الحالة
        status_label = tk.Label(filters_frame, text="📊 الحالة:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        status_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        status_combo = ttk.Combobox(filters_frame, textvariable=self.filter_status_var, 
                                   values=["جميع الحالات", "نشط", "غير نشط"], 
                                   state="readonly", width=12)
        status_combo.pack(side=tk.LEFT, padx=5, pady=15)
        status_combo.bind('<<ComboboxSelected>>', self.filter_users)
    
    def create_stats_bar(self, parent):
        """إنشاء شريط الإحصائيات"""
        stats_frame = tk.Frame(parent, bg=self.colors['dark'], height=50)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        stats_frame.pack_propagate(False)
        
        # إحصائيات المستخدمين
        stats = self.get_users_stats()
        
        # إجمالي المستخدمين
        total_label = tk.Label(stats_frame, text=f"👥 إجمالي: {stats['total']}", 
                              font=self.fonts['body'], bg=self.colors['dark'], 
                              fg=self.colors['white'])
        total_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # المستخدمين النشطين
        active_label = tk.Label(stats_frame, text=f"✅ نشط: {stats['active']}", 
                               font=self.fonts['body'], bg=self.colors['dark'], 
                               fg=self.colors['success'])
        active_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # المستخدمين غير النشطين
        inactive_label = tk.Label(stats_frame, text=f"❌ غير نشط: {stats['inactive']}", 
                                 font=self.fonts['body'], bg=self.colors['dark'], 
                                 fg=self.colors['danger'])
        inactive_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # المديرين
        admins_label = tk.Label(stats_frame, text=f"👑 مديرين: {stats['admins']}", 
                               font=self.fonts['body'], bg=self.colors['dark'], 
                               fg=self.colors['warning'])
        admins_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # آخر تسجيل دخول
        last_login_label = tk.Label(stats_frame, text=f"🕐 آخر دخول: {stats['last_login']}", 
                                   font=self.fonts['small'], bg=self.colors['dark'], 
                                   fg=self.colors['light'])
        last_login_label.pack(side=tk.RIGHT, padx=20, pady=12)
    
    def create_users_table(self, parent):
        """إنشاء جدول المستخدمين"""
        table_frame = tk.Frame(parent, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # أعمدة الجدول
        columns = ("ID", "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الهاتف", 
                  "الدور", "القسم", "الحالة", "آخر دخول", "تاريخ الإنشاء")
        
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين والعرض
        column_widths = {
            "ID": 50, "اسم المستخدم": 120, "الاسم الكامل": 150, "البريد الإلكتروني": 180,
            "الهاتف": 120, "الدور": 100, "القسم": 120, "الحالة": 80, 
            "آخر دخول": 140, "تاريخ الإنشاء": 140
        }
        
        for col in columns:
            self.users_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.users_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # شريط التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.users_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.users_tree.xview)
        
        self.users_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.users_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # قائمة السياق
        self.create_context_menu()
        
        # ربط الأحداث
        self.users_tree.bind('<Double-1>', self.edit_user)
        self.users_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="👁️ عرض الملف الشخصي", command=self.view_user_profile)
        self.context_menu.add_command(label="✏️ تعديل", command=self.edit_user)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🔑 إعادة تعيين كلمة المرور", command=self.reset_password)
        self.context_menu.add_command(label="🔒 تعطيل الحساب", command=self.deactivate_user)
        self.context_menu.add_command(label="🔓 تفعيل الحساب", command=self.activate_user)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📊 تقرير النشاط", command=self.user_activity_report)
        self.context_menu.add_command(label="📋 المشاريع والمهام", command=self.user_projects_tasks)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ حذف", command=self.delete_user)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(parent, bg=self.colors['dark'], height=30)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="جاري التحميل...", 
                                    font=self.fonts['small'], bg=self.colors['dark'], 
                                    fg=self.colors['white'])
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # معلومات إضافية
        self.info_label = tk.Label(status_frame, text="", 
                                  font=self.fonts['small'], bg=self.colors['dark'], 
                                  fg=self.colors['light'])
        self.info_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def get_roles_list(self):
        """جلب قائمة الأدوار"""
        try:
            roles = self.db_manager.cursor.execute('''
                SELECT DISTINCT role FROM users WHERE role IS NOT NULL ORDER BY role
            ''').fetchall()
            role_names = ["جميع الأدوار"] + [self.get_role_name_arabic(role[0]) for role in roles]
            return role_names
        except:
            return ["جميع الأدوار"]
    
    def get_departments_list(self):
        """جلب قائمة الأقسام"""
        try:
            departments = self.db_manager.cursor.execute('''
                SELECT DISTINCT department FROM users WHERE department IS NOT NULL ORDER BY department
            ''').fetchall()
            dept_names = ["جميع الأقسام"] + [dept[0] for dept in departments]
            return dept_names
        except:
            return ["جميع الأقسام"]
    
    def get_role_name_arabic(self, role):
        """تحويل اسم الدور إلى العربية"""
        roles = {
            'Admin': 'مدير النظام',
            'ProjectManager': 'مدير مشروع',
            'Employee': 'موظف',
            'SystemManager': 'مشرف النظام'
        }
        return roles.get(role, role)
    
    def get_users_stats(self):
        """الحصول على إحصائيات المستخدمين"""
        try:
            # إجمالي المستخدمين
            total = self.db_manager.cursor.execute("SELECT COUNT(*) FROM users").fetchone()[0]
            
            # المستخدمين النشطين
            active = self.db_manager.cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1").fetchone()[0]
            
            # المستخدمين غير النشطين
            inactive = total - active
            
            # المديرين
            admins = self.db_manager.cursor.execute("SELECT COUNT(*) FROM users WHERE role IN ('Admin', 'SystemManager')").fetchone()[0]
            
            # آخر تسجيل دخول
            last_login = self.db_manager.cursor.execute('''
                SELECT MAX(last_login) FROM users WHERE last_login IS NOT NULL
            ''').fetchone()[0]
            
            last_login_text = "غير متوفر"
            if last_login:
                try:
                    last_login_dt = datetime.fromisoformat(last_login.replace('Z', '+00:00'))
                    last_login_text = last_login_dt.strftime("%Y-%m-%d %H:%M")
                except:
                    last_login_text = str(last_login)[:16]
            
            return {
                'total': total,
                'active': active,
                'inactive': inactive,
                'admins': admins,
                'last_login': last_login_text
            }
        except Exception as e:
            return {
                'total': 0,
                'active': 0,
                'inactive': 0,
                'admins': 0,
                'last_login': 'خطأ'
            }

    def load_users(self):
        """تحميل قائمة المستخدمين"""
        try:
            # مسح البيانات الحالية
            for item in self.users_tree.get_children():
                self.users_tree.delete(item)

            # استعلام المستخدمين
            query = """
                SELECT id, username, full_name, email, phone, role, department,
                       is_active, last_login, created_date
                FROM users
                ORDER BY created_date DESC
            """

            users = self.db_manager.cursor.execute(query).fetchall()

            # إضافة البيانات للجدول
            for user in users:
                # تنسيق البيانات
                user_id = user[0]
                username = user[1] if user[1] else ""
                full_name = user[2] if user[2] else ""
                email = user[3] if user[3] else ""
                phone = user[4] if user[4] else ""
                role = self.get_role_name_arabic(user[5]) if user[5] else ""
                department = user[6] if user[6] else ""
                status = "نشط" if user[7] else "غير نشط"
                last_login = user[8][:16] if user[8] else "لم يسجل دخول"
                created_date = user[9][:16] if user[9] else ""

                # إضافة الصف
                item = self.users_tree.insert("", tk.END, values=(
                    user_id, username, full_name, email, phone, role,
                    department, status, last_login, created_date
                ))

                # تلوين الصفوف حسب الحالة
                self.color_row_by_status(item, user[7])

            # تحديث شريط الحالة
            count = len(users)
            self.status_label.config(text=f"تم تحميل {count} مستخدم")

            # إحصائيات سريعة
            active_count = len([u for u in users if u[7]])
            inactive_count = count - active_count
            self.info_label.config(text=f"نشط: {active_count} | غير نشط: {inactive_count}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين:\n{str(e)}")
            self.status_label.config(text="خطأ في التحميل")

    def color_row_by_status(self, item, is_active):
        """تلوين الصف حسب الحالة"""
        if is_active:
            # نشط - أخضر فاتح
            self.users_tree.set(item, "الحالة", "✅ نشط")
        else:
            # غير نشط - أحمر فاتح
            self.users_tree.set(item, "الحالة", "❌ غير نشط")

    def search_users(self, event=None):
        """البحث في المستخدمين"""
        search_term = self.search_var.get().strip().lower()

        # إخفاء/إظهار الصفوف حسب البحث
        for item in self.users_tree.get_children():
            values = self.users_tree.item(item)['values']
            # البحث في اسم المستخدم والاسم الكامل والبريد الإلكتروني
            if (search_term in str(values[1]).lower() or
                search_term in str(values[2]).lower() or
                search_term in str(values[3]).lower()):
                self.users_tree.reattach(item, '', tk.END)
            else:
                self.users_tree.detach(item)

    def filter_users(self, event=None):
        """فلترة المستخدمين"""
        role_filter = self.filter_role_var.get()
        dept_filter = self.filter_department_var.get()
        status_filter = self.filter_status_var.get()

        for item in self.users_tree.get_children():
            values = self.users_tree.item(item)['values']
            show_item = True

            # فلتر الدور
            if role_filter != "جميع الأدوار":
                if role_filter not in str(values[5]):
                    show_item = False

            # فلتر القسم
            if dept_filter != "جميع الأقسام":
                if dept_filter not in str(values[6]):
                    show_item = False

            # فلتر الحالة
            if status_filter != "جميع الحالات":
                if status_filter not in str(values[7]):
                    show_item = False

            if show_item:
                self.users_tree.reattach(item, '', tk.END)
            else:
                self.users_tree.detach(item)

    def sort_by_column(self, column):
        """ترتيب الجدول حسب العمود"""
        items = [(self.users_tree.set(item, column), item) for item in self.users_tree.get_children('')]

        # ترتيب البيانات
        items.sort()

        # إعادة ترتيب العناصر
        for index, (value, item) in enumerate(items):
            self.users_tree.move(item, '', index)

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        # تحديد العنصر المحدد
        item = self.users_tree.identify_row(event.y)
        if item:
            self.users_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def add_user(self):
        """إضافة مستخدم جديد"""
        self.user_form()

    def edit_user(self, event=None):
        """تعديل مستخدم محدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return

        user_id = self.users_tree.item(selected[0])['values'][0]
        self.user_form(user_id)

    def user_form(self, user_id=None):
        """نموذج إضافة/تعديل المستخدم"""
        is_edit = user_id is not None

        # إنشاء النافذة
        form_window = tk.Toplevel(self.parent)
        title = "تعديل مستخدم" if is_edit else "إضافة مستخدم جديد"
        form_window.title(title)
        form_window.geometry("700x600")
        form_window.resizable(False, False)
        form_window.configure(bg=self.colors['light'])
        form_window.transient(self.parent)
        form_window.grab_set()

        # توسيط النافذة
        self.center_window(form_window, 700, 600)

        # الإطار الرئيسي
        main_frame = tk.Frame(form_window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text=f"👥 {title}",
                              font=self.fonts['heading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(pady=15)

        # النموذج
        form_frame = tk.Frame(main_frame, bg=self.colors['white'])
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # متغيرات النموذج
        username_var = tk.StringVar()
        password_var = tk.StringVar()
        confirm_password_var = tk.StringVar()
        full_name_var = tk.StringVar()
        email_var = tk.StringVar()
        phone_var = tk.StringVar()
        role_var = tk.StringVar(value="Employee")
        department_var = tk.StringVar()
        is_active_var = tk.BooleanVar(value=True)
        notes_var = tk.StringVar()

        # الحقول
        fields = [
            ("اسم المستخدم *:", username_var, "entry"),
            ("كلمة المرور *:", password_var, "password"),
            ("تأكيد كلمة المرور *:", confirm_password_var, "password"),
            ("الاسم الكامل *:", full_name_var, "entry"),
            ("البريد الإلكتروني:", email_var, "entry"),
            ("رقم الهاتف:", phone_var, "entry"),
            ("الدور:", role_var, "combo", ["Employee", "ProjectManager", "SystemManager", "Admin"]),
            ("القسم:", department_var, "combo", ["الهندسة", "تقنية المعلومات", "الإشراف", "الإدارة", "المالية"]),
            ("الحساب نشط:", is_active_var, "checkbox"),
            ("ملاحظات:", notes_var, "text")
        ]

        widgets = {}
        for i, field in enumerate(fields):
            field_frame = tk.Frame(form_frame, bg=self.colors['white'])
            field_frame.pack(fill=tk.X, padx=20, pady=8)

            label = tk.Label(field_frame, text=field[0], font=self.fonts['body'],
                           bg=self.colors['white'], fg=self.colors['dark'])
            label.pack(anchor=tk.W, pady=(0, 3))

            if field[2] == "entry":
                widget = tk.Entry(field_frame, textvariable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], width=50)
                widget.pack(anchor=tk.W, ipady=3)
            elif field[2] == "password":
                widget = tk.Entry(field_frame, textvariable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], width=50, show="*")
                widget.pack(anchor=tk.W, ipady=3)
            elif field[2] == "combo":
                widget = ttk.Combobox(field_frame, textvariable=field[1], values=field[3],
                                    font=self.fonts['body'], width=47, state="readonly")
                widget.pack(anchor=tk.W, ipady=3)
            elif field[2] == "checkbox":
                widget = tk.Checkbutton(field_frame, variable=field[1], font=self.fonts['body'],
                                      bg=self.colors['white'], text="تفعيل الحساب")
                widget.pack(anchor=tk.W)
            elif field[2] == "text":
                widget = tk.Text(field_frame, height=3, font=self.fonts['body'],
                               bg=self.colors['light'], width=50)
                widget.pack(anchor=tk.W)

            widgets[field[0]] = widget

        # إخفاء حقول كلمة المرور في وضع التعديل
        if is_edit:
            widgets["كلمة المرور *:"].master.pack_forget()
            widgets["تأكيد كلمة المرور *:"].master.pack_forget()

        # تحميل البيانات في حالة التعديل
        if is_edit:
            try:
                user = self.db_manager.cursor.execute('''
                    SELECT username, full_name, email, phone, role, department, is_active, notes
                    FROM users WHERE id = ?
                ''', (user_id,)).fetchone()

                if user:
                    username_var.set(user[0] or "")
                    full_name_var.set(user[1] or "")
                    email_var.set(user[2] or "")
                    phone_var.set(user[3] or "")
                    role_var.set(user[4] or "Employee")
                    department_var.set(user[5] or "")
                    is_active_var.set(bool(user[6]))
                    if user[7]:
                        widgets["ملاحظات:"].insert("1.0", user[7])
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل بيانات المستخدم:\n{str(e)}")

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(main_frame, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X)

        def save_user():
            # التحقق من البيانات المطلوبة
            username = username_var.get().strip()
            full_name = full_name_var.get().strip()

            if not username:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
                return

            if not full_name:
                messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
                return

            if not is_edit:
                password = password_var.get()
                confirm_password = confirm_password_var.get()

                if not password:
                    messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                    return

                if password != confirm_password:
                    messagebox.showerror("خطأ", "كلمات المرور غير متطابقة")
                    return

            try:
                # جمع البيانات
                email = email_var.get().strip()
                phone = phone_var.get().strip()
                role = role_var.get()
                department = department_var.get()
                is_active = is_active_var.get()
                notes = widgets["ملاحظات:"].get("1.0", tk.END).strip()

                if is_edit:
                    # تحديث المستخدم
                    self.db_manager.cursor.execute('''
                        UPDATE users SET
                            username = ?, full_name = ?, email = ?, phone = ?,
                            role = ?, department = ?, is_active = ?, notes = ?
                        WHERE id = ?
                    ''', (username, full_name, email, phone, role, department,
                          is_active, notes, user_id))
                    message = "تم تحديث المستخدم بنجاح"
                else:
                    # التحقق من عدم وجود اسم المستخدم
                    existing = self.db_manager.cursor.execute(
                        "SELECT id FROM users WHERE username = ?", (username,)
                    ).fetchone()

                    if existing:
                        messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                        return

                    # تشفير كلمة المرور
                    hashed_password = hashlib.sha256(password.encode()).hexdigest()

                    # إضافة مستخدم جديد
                    self.db_manager.cursor.execute('''
                        INSERT INTO users (
                            username, password, full_name, email, phone, role,
                            department, is_active, notes, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (username, hashed_password, full_name, email, phone, role,
                          department, is_active, notes, self.current_user['id']))
                    message = "تم إضافة المستخدم بنجاح"

                self.db_manager.conn.commit()
                messagebox.showinfo("نجح", message)
                form_window.destroy()
                self.load_users()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المستخدم:\n{str(e)}")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ",
                           font=self.fonts['button'], bg=self.colors['success'],
                           fg=self.colors['white'], relief=tk.FLAT,
                           command=save_user, cursor="hand2")
        save_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                             font=self.fonts['button'], bg=self.colors['danger'],
                             fg=self.colors['white'], relief=tk.FLAT,
                             command=form_window.destroy, cursor="hand2")
        cancel_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

    def center_window(self, window, width, height):
        """توسيط النافذة"""
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"{width}x{height}+{x}+{y}")

    def view_user_profile(self):
        """عرض الملف الشخصي للمستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لعرض ملفه الشخصي")
            return

        user_id = self.users_tree.item(selected[0])['values'][0]
        messagebox.showinfo("الملف الشخصي", f"عرض الملف الشخصي للمستخدم رقم {user_id}\n(قيد التطوير)")

    def reset_password(self):
        """إعادة تعيين كلمة المرور"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لإعادة تعيين كلمة مروره")
            return

        user_id = self.users_tree.item(selected[0])['values'][0]
        username = self.users_tree.item(selected[0])['values'][1]

        # تأكيد العملية
        result = messagebox.askyesno(
            "إعادة تعيين كلمة المرور",
            f"هل أنت متأكد من إعادة تعيين كلمة المرور للمستخدم '{username}'؟\n\nسيتم تعيين كلمة المرور إلى: 123456"
        )

        if result:
            try:
                # تعيين كلمة مرور افتراضية
                new_password = "123456"
                hashed_password = hashlib.sha256(new_password.encode()).hexdigest()

                self.db_manager.cursor.execute('''
                    UPDATE users SET password = ? WHERE id = ?
                ''', (hashed_password, user_id))

                self.db_manager.conn.commit()

                messagebox.showinfo("نجح", f"تم إعادة تعيين كلمة المرور بنجاح\nكلمة المرور الجديدة: {new_password}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في إعادة تعيين كلمة المرور:\n{str(e)}")

    def activate_user(self):
        """تفعيل حساب المستخدم"""
        self.toggle_user_status(True)

    def deactivate_user(self):
        """تعطيل حساب المستخدم"""
        self.toggle_user_status(False)

    def toggle_user_status(self, status):
        """تبديل حالة المستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم")
            return

        user_id = self.users_tree.item(selected[0])['values'][0]
        username = self.users_tree.item(selected[0])['values'][1]

        # منع تعطيل المستخدم الحالي
        if user_id == self.current_user['id'] and not status:
            messagebox.showwarning("تحذير", "لا يمكنك تعطيل حسابك الخاص")
            return

        action = "تفعيل" if status else "تعطيل"
        result = messagebox.askyesno(
            f"{action} المستخدم",
            f"هل أنت متأكد من {action} المستخدم '{username}'؟"
        )

        if result:
            try:
                self.db_manager.cursor.execute('''
                    UPDATE users SET is_active = ? WHERE id = ?
                ''', (status, user_id))

                self.db_manager.conn.commit()

                messagebox.showinfo("نجح", f"تم {action} المستخدم بنجاح")
                self.load_users()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في {action} المستخدم:\n{str(e)}")

    def user_activity_report(self):
        """تقرير نشاط المستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لعرض تقرير نشاطه")
            return

        user_id = self.users_tree.item(selected[0])['values'][0]
        messagebox.showinfo("تقرير النشاط", f"تقرير نشاط المستخدم رقم {user_id}\n(قيد التطوير)")

    def user_projects_tasks(self):
        """مشاريع ومهام المستخدم"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم لعرض مشاريعه ومهامه")
            return

        user_id = self.users_tree.item(selected[0])['values'][0]
        messagebox.showinfo("المشاريع والمهام", f"مشاريع ومهام المستخدم رقم {user_id}\n(قيد التطوير)")

    def delete_user(self):
        """حذف مستخدم محدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return

        user_id = self.users_tree.item(selected[0])['values'][0]
        username = self.users_tree.item(selected[0])['values'][1]

        # منع حذف المستخدم الحالي
        if user_id == self.current_user['id']:
            messagebox.showwarning("تحذير", "لا يمكنك حذف حسابك الخاص")
            return

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستخدم '{username}'؟\n\nسيتم حذف جميع البيانات المرتبطة.\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                # حذف المستخدم والبيانات المرتبطة
                self.db_manager.cursor.execute("DELETE FROM activity_log WHERE user_id = ?", (user_id,))
                self.db_manager.cursor.execute("DELETE FROM comments WHERE user_id = ?", (user_id,))
                self.db_manager.cursor.execute("DELETE FROM users WHERE id = ?", (user_id,))

                self.db_manager.conn.commit()

                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                self.load_users()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المستخدم:\n{str(e)}")

    def export_users(self):
        """تصدير قائمة المستخدمين"""
        messagebox.showinfo("تصدير المستخدمين", "تصدير قائمة المستخدمين\n(قيد التطوير)")

    def import_users(self):
        """استيراد قائمة المستخدمين"""
        messagebox.showinfo("استيراد المستخدمين", "استيراد قائمة المستخدمين\n(قيد التطوير)")
