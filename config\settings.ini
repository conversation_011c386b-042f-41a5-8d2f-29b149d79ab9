[DATABASE]
# إعدادات قاعدة البيانات
# Database Settings
driver = Microsoft Access Driver (*.mdb, *.accdb)
database_path = database/kufranja_projects.accdb
backup_interval = 24
max_connections = 10
connection_timeout = 30

[APPLICATION]
# إعدادات التطبيق
# Application Settings
app_name = نظام إدارة المشاريع الهندسية - بلدية كفرنجة
app_name_en = Engineering Project Management System - Kufranja Municipality
version = 1.0.0
language = ar
theme = modern
window_state = maximized
auto_save = true
auto_save_interval = 5

[REPORTS]
# إعدادات التقارير
# Reports Settings
default_template = standard
output_directory = reports
auto_backup = true
include_logo = true
logo_path = assets/logo.png
default_format = PDF
max_report_size = 50MB

[SECURITY]
# إعدادات الأمان
# Security Settings
session_timeout = 30
password_min_length = 8
password_require_uppercase = true
password_require_lowercase = true
password_require_numbers = true
password_require_symbols = false
max_login_attempts = 3
lockout_duration = 15
enable_activity_log = true
enable_audit_trail = true

[LOGGING]
# إعدادات السجلات
# Logging Settings
log_level = INFO
log_file = logs/application.log
database_log_file = logs/database.log
error_log_file = logs/errors.log
max_log_size = 10MB
max_log_files = 5
log_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
log_date_format = %Y-%m-%d %H:%M:%S

[UI]
# إعدادات واجهة المستخدم
# User Interface Settings
font_family = Segoe UI
font_size = 12
font_size_title = 16
font_size_header = 14
color_primary = #1f538d
color_secondary = #2980b9
color_success = #27ae60
color_warning = #f39c12
color_danger = #e74c3c
color_light = #ecf0f1
color_dark = #2c3e50
sidebar_width = 250
header_height = 80

[BACKUP]
# إعدادات النسخ الاحتياطية
# Backup Settings
auto_backup = true
backup_interval = 24
backup_directory = backup
max_backup_files = 30
backup_compression = true
backup_encryption = false

[NOTIFICATIONS]
# إعدادات الإشعارات
# Notifications Settings
enable_notifications = true
show_system_notifications = true
notification_duration = 5
enable_email_notifications = false
email_server = 
email_port = 587
email_username = 
email_password = 
email_use_tls = true

[PERFORMANCE]
# إعدادات الأداء
# Performance Settings
cache_enabled = true
cache_size = 100MB
database_pool_size = 5
query_timeout = 30
enable_compression = true
lazy_loading = true

[INTEGRATION]
# إعدادات التكامل
# Integration Settings
enable_api = false
api_port = 8080
enable_web_interface = false
web_port = 8000
enable_mobile_sync = false

[MAINTENANCE]
# إعدادات الصيانة
# Maintenance Settings
auto_cleanup = true
cleanup_interval = 7
temp_file_retention = 3
log_retention = 30
report_retention = 365
enable_health_check = true
health_check_interval = 60

[LOCALIZATION]
# إعدادات التوطين
# Localization Settings
default_language = ar
supported_languages = ar,en
date_format = yyyy/mm/dd
time_format = HH:MM
currency_symbol = ريال
currency_position = after
number_format = #,##0.00
rtl_support = true

[EXPORT]
# إعدادات التصدير
# Export Settings
default_export_format = PDF
pdf_quality = high
excel_format = xlsx
word_format = docx
image_quality = 300
compress_exports = true
watermark_enabled = false
watermark_text = بلدية كفرنجة

[IMPORT]
# إعدادات الاستيراد
# Import Settings
max_import_size = 100MB
allowed_file_types = xlsx,csv,txt
validate_data = true
skip_duplicates = true
create_backup_before_import = true

[ADVANCED]
# إعدادات متقدمة
# Advanced Settings
debug_mode = false
developer_mode = false
enable_profiling = false
memory_limit = 1GB
thread_pool_size = 4
enable_plugins = false
plugin_directory = plugins
