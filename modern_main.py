#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشاريع الهندسية العصري - بلدية كفرنجة
Modern Engineering Project Management System - Kufranja Municipality

نسخة عصرية مع واجهة مستخدم متقدمة وميزات شاملة
Modern version with advanced UI and comprehensive features
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import hashlib
from datetime import datetime, date
import os
import shutil
from pathlib import Path
import json
import webbrowser
from PIL import Image, ImageTk
import subprocess

class ModernProjectManager:
    """نظام إدارة المشاريع العصري"""
    
    def __init__(self):
        """تهيئة النظام"""
        self.setup_directories()
        self.setup_database()
        self.setup_theme()
        self.current_user = None
        self.create_splash_screen()
    
    def setup_directories(self):
        """إعداد المجلدات المطلوبة"""
        directories = [
            "database", "attachments", "reports", "logs", 
            "config", "backup", "temp", "assets", "themes"
        ]
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.db_path = "database/kufranja_modern.db"
        self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
        self.cursor = self.conn.cursor()
        self.create_tables()
        self.insert_default_data()
    
    def setup_theme(self):
        """إعداد المظهر العصري"""
        self.colors = {
            'primary': '#2C3E50',      # أزرق داكن
            'secondary': '#3498DB',    # أزرق فاتح
            'success': '#27AE60',      # أخضر
            'warning': '#F39C12',      # برتقالي
            'danger': '#E74C3C',       # أحمر
            'light': '#ECF0F1',        # رمادي فاتح
            'dark': '#34495E',         # رمادي داكن
            'white': '#FFFFFF',        # أبيض
            'accent': '#9B59B6',       # بنفسجي
            'info': '#17A2B8'          # أزرق معلوماتي
        }
        
        self.fonts = {
            'title': ('Segoe UI', 20, 'bold'),
            'heading': ('Segoe UI', 16, 'bold'),
            'subheading': ('Segoe UI', 14, 'bold'),
            'body': ('Segoe UI', 12),
            'small': ('Segoe UI', 10),
            'button': ('Segoe UI', 11, 'bold')
        }
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين المحدث
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                role TEXT NOT NULL,
                department TEXT,
                avatar_path TEXT,
                is_active INTEGER DEFAULT 1,
                last_login TIMESTAMP,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                notes TEXT
            )
        ''')
        
        # جدول المشاريع المحدث
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                project_type TEXT,
                location TEXT,
                start_date DATE,
                end_date DATE,
                planned_budget REAL,
                actual_budget REAL DEFAULT 0,
                status TEXT DEFAULT 'مخطط',
                priority TEXT DEFAULT 'متوسط',
                completion_percentage REAL DEFAULT 0,
                project_manager INTEGER,
                contractor TEXT,
                tags TEXT,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (project_manager) REFERENCES users (id)
            )
        ''')
        
        # جدول المهام المحدث
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                assigned_to INTEGER,
                start_date DATE,
                end_date DATE,
                estimated_hours REAL,
                actual_hours REAL DEFAULT 0,
                status TEXT DEFAULT 'غير مبدوء',
                priority TEXT DEFAULT 'متوسط',
                completion_percentage REAL DEFAULT 0,
                dependencies TEXT,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (assigned_to) REFERENCES users (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # جدول المرفقات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_type TEXT NOT NULL,
                entity_id INTEGER NOT NULL,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                file_type TEXT,
                uploaded_by INTEGER,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT,
                FOREIGN KEY (uploaded_by) REFERENCES users (id)
            )
        ''')
        
        # جدول التعليقات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS comments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_type TEXT NOT NULL,
                entity_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                comment TEXT NOT NULL,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول سجل النشاطات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                entity_type TEXT,
                entity_id INTEGER,
                description TEXT,
                ip_address TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول الإعدادات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                modified_by INTEGER,
                modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (modified_by) REFERENCES users (id)
            )
        ''')
        
        # جدول الإشعارات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS notifications (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                title TEXT NOT NULL,
                message TEXT NOT NULL,
                type TEXT DEFAULT 'info',
                is_read INTEGER DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        self.conn.commit()
    
    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        # التحقق من وجود المستخدم الافتراضي
        self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if self.cursor.fetchone()[0] == 0:
            # إضافة المستخدم الافتراضي
            hashed_password = hashlib.sha256("admin123".encode()).hexdigest()
            self.cursor.execute('''
                INSERT INTO users (username, password, full_name, email, role, department)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ("admin", hashed_password, "مدير النظام", "<EMAIL>", "Admin", "تقنية المعلومات"))
            
            # إضافة مستخدمين تجريبيين
            users_data = [
                ("manager1", hashlib.sha256("manager123".encode()).hexdigest(), "أحمد محمد", "<EMAIL>", "ProjectManager", "الهندسة"),
                ("engineer1", hashlib.sha256("eng123".encode()).hexdigest(), "فاطمة علي", "<EMAIL>", "Employee", "الهندسة"),
                ("supervisor1", hashlib.sha256("super123".encode()).hexdigest(), "محمد خالد", "<EMAIL>", "SystemManager", "الإشراف")
            ]
            
            for user in users_data:
                self.cursor.execute('''
                    INSERT INTO users (username, password, full_name, email, role, department)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', user)
            
            # إضافة مشاريع تجريبية
            projects_data = [
                ("مشروع تطوير الطرق الرئيسية", "تطوير وصيانة الطرق الرئيسية في البلدية", "بنية تحتية", "كفرنجة", 500000, "قيد التنفيذ", "عالي", 65.5, 2),
                ("مشروع تحديث شبكة الإنارة", "تحديث وتطوير شبكة الإنارة العامة", "كهرباء", "المدينة", 250000, "مخطط", "متوسط", 0, 2),
                ("مشروع تطوير الحدائق العامة", "إنشاء وتطوير الحدائق والمساحات الخضراء", "بيئة", "الأحياء السكنية", 180000, "مكتمل", "منخفض", 100, 3)
            ]
            
            for project in projects_data:
                self.cursor.execute('''
                    INSERT INTO projects (name, description, project_type, location, planned_budget, status, priority, completion_percentage, project_manager, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', project + (1,))
            
            # إضافة إعدادات افتراضية
            settings_data = [
                ("app_name", "نظام إدارة المشاريع الهندسية - بلدية كفرنجة", "اسم التطبيق"),
                ("app_version", "2.0.0", "إصدار التطبيق"),
                ("theme", "modern", "مظهر التطبيق"),
                ("language", "ar", "لغة التطبيق"),
                ("backup_interval", "24", "فترة النسخ الاحتياطي بالساعات"),
                ("session_timeout", "30", "انتهاء الجلسة بالدقائق")
            ]
            
            for setting in settings_data:
                self.cursor.execute('''
                    INSERT INTO settings (key, value, description, modified_by)
                    VALUES (?, ?, ?, ?)
                ''', setting + (1,))
            
            self.conn.commit()
    
    def create_splash_screen(self):
        """إنشاء شاشة البداية"""
        self.splash = tk.Tk()
        self.splash.title("نظام إدارة المشاريع الهندسية")
        self.splash.geometry("600x400")
        self.splash.resizable(False, False)
        self.splash.configure(bg=self.colors['primary'])
        
        # إزالة شريط العنوان
        self.splash.overrideredirect(True)
        
        # توسيط النافذة
        self.center_window(self.splash, 600, 400)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.splash, bg=self.colors['primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شعار البلدية (نص مؤقت)
        logo_frame = tk.Frame(main_frame, bg=self.colors['primary'])
        logo_frame.pack(expand=True)
        
        # أيقونة كبيرة
        icon_label = tk.Label(logo_frame, text="🏗️", font=('Arial', 80), 
                             bg=self.colors['primary'], fg=self.colors['white'])
        icon_label.pack(pady=(50, 20))
        
        # عنوان النظام
        title_label = tk.Label(logo_frame, text="نظام إدارة المشاريع الهندسية", 
                              font=self.fonts['title'], bg=self.colors['primary'], 
                              fg=self.colors['white'])
        title_label.pack(pady=(0, 10))
        
        # اسم البلدية
        subtitle_label = tk.Label(logo_frame, text="بلدية كفرنجة الجديدة", 
                                 font=self.fonts['heading'], bg=self.colors['primary'], 
                                 fg=self.colors['light'])
        subtitle_label.pack(pady=(0, 30))
        
        # شريط التحميل
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(logo_frame, variable=self.progress_var, 
                                      maximum=100, length=300, mode='determinate')
        progress_bar.pack(pady=(0, 20))
        
        # نص التحميل
        self.loading_label = tk.Label(logo_frame, text="جاري التحميل...", 
                                     font=self.fonts['body'], bg=self.colors['primary'], 
                                     fg=self.colors['light'])
        self.loading_label.pack()
        
        # معلومات الإصدار
        version_label = tk.Label(main_frame, text="الإصدار 2.0.0 | تطوير: فريق تقنية المعلومات", 
                                font=self.fonts['small'], bg=self.colors['primary'], 
                                fg=self.colors['light'])
        version_label.pack(side=tk.BOTTOM, pady=20)
        
        # بدء عملية التحميل
        self.splash.after(100, self.loading_animation)
    
    def loading_animation(self):
        """رسوم متحركة للتحميل"""
        steps = [
            (20, "تهيئة قاعدة البيانات..."),
            (40, "تحميل الإعدادات..."),
            (60, "فحص التحديثات..."),
            (80, "إعداد الواجهة..."),
            (100, "اكتمل التحميل!")
        ]
        
        current_step = 0
        
        def update_progress():
            nonlocal current_step
            if current_step < len(steps):
                progress, text = steps[current_step]
                self.progress_var.set(progress)
                self.loading_label.config(text=text)
                current_step += 1
                self.splash.after(800, update_progress)
            else:
                self.splash.after(1000, self.show_login)
        
        update_progress()
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.splash.destroy()
        self.create_login_window()
    
    def center_window(self, window, width, height):
        """توسيط النافذة على الشاشة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_login_window(self):
        """إنشاء نافذة تسجيل الدخول العصرية"""
        self.login_window = tk.Tk()
        self.login_window.title("تسجيل الدخول - نظام إدارة المشاريع الهندسية")
        self.login_window.geometry("500x650")
        self.login_window.resizable(False, False)
        self.login_window.configure(bg=self.colors['light'])
        
        # توسيط النافذة
        self.center_window(self.login_window, 500, 650)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.login_window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)
        
        # إطار الهيدر
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=120)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        header_frame.pack_propagate(False)
        
        # أيقونة النظام
        icon_label = tk.Label(header_frame, text="🏗️", font=('Arial', 40), 
                             bg=self.colors['primary'], fg=self.colors['white'])
        icon_label.pack(pady=(20, 5))
        
        # عنوان النظام
        title_label = tk.Label(header_frame, text="نظام إدارة المشاريع الهندسية", 
                              font=self.fonts['heading'], bg=self.colors['primary'], 
                              fg=self.colors['white'])
        title_label.pack()
        
        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        login_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # عنوان تسجيل الدخول
        login_title = tk.Label(login_frame, text="تسجيل الدخول", 
                              font=self.fonts['subheading'], bg=self.colors['white'], 
                              fg=self.colors['dark'])
        login_title.pack(pady=(30, 20))
        
        # حقل اسم المستخدم
        username_frame = tk.Frame(login_frame, bg=self.colors['white'])
        username_frame.pack(fill=tk.X, padx=30, pady=(0, 15))
        
        tk.Label(username_frame, text="👤 اسم المستخدم", font=self.fonts['body'], 
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))
        
        self.username_entry = tk.Entry(username_frame, font=self.fonts['body'], 
                                      relief=tk.FLAT, bd=5, bg=self.colors['light'])
        self.username_entry.pack(fill=tk.X, ipady=8)
        
        # حقل كلمة المرور
        password_frame = tk.Frame(login_frame, bg=self.colors['white'])
        password_frame.pack(fill=tk.X, padx=30, pady=(0, 25))
        
        tk.Label(password_frame, text="🔒 كلمة المرور", font=self.fonts['body'], 
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))
        
        self.password_entry = tk.Entry(password_frame, font=self.fonts['body'], 
                                      show="*", relief=tk.FLAT, bd=5, bg=self.colors['light'])
        self.password_entry.pack(fill=tk.X, ipady=8)
        
        # زر تسجيل الدخول
        login_button = tk.Button(login_frame, text="🚀 تسجيل الدخول", 
                                font=self.fonts['button'], bg=self.colors['secondary'], 
                                fg=self.colors['white'], relief=tk.FLAT, 
                                command=self.login, cursor="hand2")
        login_button.pack(fill=tk.X, padx=30, pady=(0, 20), ipady=12)
        
        # خيارات إضافية
        options_frame = tk.Frame(login_frame, bg=self.colors['white'])
        options_frame.pack(fill=tk.X, padx=30, pady=(0, 30))
        
        # تذكرني
        self.remember_var = tk.BooleanVar()
        remember_check = tk.Checkbutton(options_frame, text="تذكرني", 
                                       variable=self.remember_var, bg=self.colors['white'], 
                                       font=self.fonts['small'])
        remember_check.pack(side=tk.LEFT)
        
        # نسيت كلمة المرور
        forgot_label = tk.Label(options_frame, text="نسيت كلمة المرور؟", 
                               font=self.fonts['small'], bg=self.colors['white'], 
                               fg=self.colors['secondary'], cursor="hand2")
        forgot_label.pack(side=tk.RIGHT)
        forgot_label.bind("<Button-1>", self.forgot_password)
        
        # معلومات المستخدمين
        info_frame = tk.Frame(main_frame, bg=self.colors['light'])
        info_frame.pack(fill=tk.X)
        
        info_text = """المستخدمون المتاحون:
👨‍💼 admin / admin123 (مدير النظام)
👨‍🔧 manager1 / manager123 (مدير مشروع)
👩‍💻 engineer1 / eng123 (مهندس)
👨‍💼 supervisor1 / super123 (مشرف)"""
        
        info_label = tk.Label(info_frame, text=info_text, font=self.fonts['small'], 
                             bg=self.colors['light'], fg=self.colors['dark'], 
                             justify=tk.LEFT)
        info_label.pack(pady=10)
        
        # ربط مفتاح Enter
        self.login_window.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def forgot_password(self, event):
        """معالج نسيان كلمة المرور"""
        messagebox.showinfo("نسيت كلمة المرور", 
                           "يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور")
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        hashed_password = self.hash_password(password)
        self.cursor.execute('''
            SELECT id, username, full_name, email, role, department, avatar_path
            FROM users 
            WHERE username = ? AND password = ? AND is_active = 1
        ''', (username, hashed_password))
        
        result = self.cursor.fetchone()
        if result:
            user_data = {
                'id': result[0],
                'username': result[1],
                'full_name': result[2],
                'email': result[3],
                'role': result[4],
                'department': result[5],
                'avatar_path': result[6]
            }
            
            # تحديث وقت آخر دخول
            self.cursor.execute('''
                UPDATE users SET last_login = ? WHERE id = ?
            ''', (datetime.now(), user_data['id']))
            
            # تسجيل النشاط
            self.log_activity(user_data['id'], "تسجيل دخول", "users", user_data['id'], 
                             f"تسجيل دخول المستخدم {user_data['full_name']}")
            
            self.conn.commit()
            return user_data
        
        return None
    
    def log_activity(self, user_id, action, entity_type=None, entity_id=None, description=None):
        """تسجيل نشاط المستخدم"""
        self.cursor.execute('''
            INSERT INTO activity_log (user_id, action, entity_type, entity_id, description)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, action, entity_type, entity_id, description))
        self.conn.commit()
    
    def login(self):
        """معالج تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        user = self.authenticate_user(username, password)
        if user:
            self.current_user = user
            messagebox.showinfo("نجح تسجيل الدخول", 
                               f"مرحباً {user['full_name']}\nالصلاحية: {self.get_role_name(user['role'])}")
            self.login_window.destroy()
            self.create_main_window()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)
    
    def get_role_name(self, role):
        """الحصول على اسم الصلاحية بالعربية"""
        roles = {
            'Admin': 'مدير النظام',
            'ProjectManager': 'مدير مشروع',
            'Employee': 'موظف',
            'SystemManager': 'مشرف النظام'
        }
        return roles.get(role, role)

    def create_main_window(self):
        """إنشاء النافذة الرئيسية العصرية"""
        self.main_window = tk.Tk()
        self.main_window.title(f"نظام إدارة المشاريع الهندسية - {self.current_user['full_name']}")
        self.main_window.geometry("1400x900")
        self.main_window.state('zoomed')
        self.main_window.configure(bg=self.colors['light'])

        # إنشاء الشريط العلوي
        self.create_top_bar()

        # إنشاء الشريط الجانبي
        self.create_sidebar()

        # إنشاء المنطقة الرئيسية
        self.create_main_content_area()

        # تحميل لوحة التحكم افتراضياً
        self.show_dashboard()

        # إعداد الاختصارات
        self.setup_shortcuts()

    def create_top_bar(self):
        """إنشاء الشريط العلوي"""
        self.top_bar = tk.Frame(self.main_window, bg=self.colors['primary'], height=70)
        self.top_bar.pack(fill=tk.X)
        self.top_bar.pack_propagate(False)

        # الجانب الأيمن - معلومات النظام
        right_frame = tk.Frame(self.top_bar, bg=self.colors['primary'])
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)

        # عنوان النظام
        title_label = tk.Label(right_frame, text="🏗️ نظام إدارة المشاريع الهندسية",
                              font=self.fonts['heading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(side=tk.RIGHT, pady=15)

        # الجانب الأيسر - معلومات المستخدم والأدوات
        left_frame = tk.Frame(self.top_bar, bg=self.colors['primary'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)

        # معلومات المستخدم
        user_frame = tk.Frame(left_frame, bg=self.colors['primary'])
        user_frame.pack(side=tk.LEFT, pady=10)

        # صورة المستخدم (افتراضية)
        avatar_label = tk.Label(user_frame, text="👤", font=('Arial', 20),
                               bg=self.colors['secondary'], fg=self.colors['white'],
                               width=3, height=1)
        avatar_label.pack(side=tk.LEFT, padx=(0, 10))

        # اسم المستخدم والصلاحية
        user_info_frame = tk.Frame(user_frame, bg=self.colors['primary'])
        user_info_frame.pack(side=tk.LEFT)

        name_label = tk.Label(user_info_frame, text=f"مرحباً، {self.current_user['full_name']}",
                             font=self.fonts['body'], bg=self.colors['primary'],
                             fg=self.colors['white'])
        name_label.pack(anchor=tk.W)

        role_label = tk.Label(user_info_frame, text=f"{self.get_role_name(self.current_user['role'])} | {self.current_user['department']}",
                             font=self.fonts['small'], bg=self.colors['primary'],
                             fg=self.colors['light'])
        role_label.pack(anchor=tk.W)

        # أدوات سريعة
        tools_frame = tk.Frame(left_frame, bg=self.colors['primary'])
        tools_frame.pack(side=tk.LEFT, padx=(30, 0))

        # زر الإشعارات
        self.notifications_count = tk.StringVar(value="3")
        notif_button = tk.Button(tools_frame, text=f"🔔 {self.notifications_count.get()}",
                                font=self.fonts['small'], bg=self.colors['warning'],
                                fg=self.colors['white'], relief=tk.FLAT,
                                command=self.show_notifications, cursor="hand2")
        notif_button.pack(side=tk.LEFT, padx=5, pady=15, ipadx=10)

        # زر البحث السريع
        search_button = tk.Button(tools_frame, text="🔍 بحث",
                                 font=self.fonts['small'], bg=self.colors['info'],
                                 fg=self.colors['white'], relief=tk.FLAT,
                                 command=self.quick_search, cursor="hand2")
        search_button.pack(side=tk.LEFT, padx=5, pady=15, ipadx=10)

        # زر الإعدادات
        settings_button = tk.Button(tools_frame, text="⚙️",
                                   font=self.fonts['body'], bg=self.colors['dark'],
                                   fg=self.colors['white'], relief=tk.FLAT,
                                   command=self.show_settings, cursor="hand2")
        settings_button.pack(side=tk.LEFT, padx=5, pady=15, ipadx=10)

        # زر تسجيل الخروج
        logout_button = tk.Button(tools_frame, text="🚪 خروج",
                                 font=self.fonts['small'], bg=self.colors['danger'],
                                 fg=self.colors['white'], relief=tk.FLAT,
                                 command=self.logout, cursor="hand2")
        logout_button.pack(side=tk.LEFT, padx=5, pady=15, ipadx=10)

    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar = tk.Frame(self.main_window, bg=self.colors['dark'], width=280)
        self.sidebar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sidebar.pack_propagate(False)

        # عنوان القائمة
        menu_title = tk.Label(self.sidebar, text="📋 القائمة الرئيسية",
                             font=self.fonts['subheading'], bg=self.colors['dark'],
                             fg=self.colors['white'])
        menu_title.pack(pady=(20, 30), padx=20)

        # قائمة الوظائف
        self.menu_items = [
            ("🏠", "لوحة التحكم", "dashboard", self.colors['secondary']),
            ("🏗️", "إدارة المشاريع", "projects", self.colors['success']),
            ("📋", "إدارة المهام", "tasks", self.colors['warning']),
            ("👥", "إدارة المستخدمين", "users", self.colors['info']),
            ("📊", "التقارير والإحصائيات", "reports", self.colors['accent']),
            ("📁", "إدارة الملفات", "files", self.colors['primary']),
            ("💬", "التعليقات والملاحظات", "comments", self.colors['secondary']),
            ("📈", "تحليل الأداء", "analytics", self.colors['success']),
            ("🔧", "الإعدادات", "settings", self.colors['dark'])
        ]

        self.menu_buttons = {}
        for icon, text, key, color in self.menu_items:
            button_frame = tk.Frame(self.sidebar, bg=self.colors['dark'])
            button_frame.pack(fill=tk.X, padx=15, pady=3)

            button = tk.Button(button_frame, text=f"{icon}  {text}",
                              font=self.fonts['body'], bg=self.colors['dark'],
                              fg=self.colors['white'], relief=tk.FLAT,
                              anchor=tk.W, command=lambda k=key: self.show_section(k),
                              cursor="hand2")
            button.pack(fill=tk.X, ipady=12, ipadx=20)

            # تأثير hover
            def on_enter(e, btn=button, clr=color):
                btn.config(bg=clr)

            def on_leave(e, btn=button):
                if btn != self.active_menu_button:
                    btn.config(bg=self.colors['dark'])

            button.bind("<Enter>", on_enter)
            button.bind("<Leave>", on_leave)

            self.menu_buttons[key] = button

        self.active_menu_button = None

        # معلومات النظام في الأسفل
        info_frame = tk.Frame(self.sidebar, bg=self.colors['dark'])
        info_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=20)

        # إحصائيات سريعة
        stats_text = self.get_quick_stats()
        stats_label = tk.Label(info_frame, text=stats_text,
                              font=self.fonts['small'], bg=self.colors['dark'],
                              fg=self.colors['light'], justify=tk.LEFT)
        stats_label.pack()

        # معلومات الإصدار
        version_label = tk.Label(info_frame, text="الإصدار 2.0.0",
                                font=self.fonts['small'], bg=self.colors['dark'],
                                fg=self.colors['light'])
        version_label.pack(pady=(10, 0))

    def create_main_content_area(self):
        """إنشاء المنطقة الرئيسية للمحتوى"""
        self.content_area = tk.Frame(self.main_window, bg=self.colors['light'])
        self.content_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # إطار المحتوى الحالي
        self.current_content = None

    def get_quick_stats(self):
        """الحصول على إحصائيات سريعة"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM projects")
            total_projects = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'قيد التنفيذ'")
            active_projects = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
            active_users = self.cursor.fetchone()[0]

            return f"""📊 إحصائيات سريعة:
• المشاريع: {total_projects}
• النشطة: {active_projects}
• المستخدمين: {active_users}"""
        except:
            return "📊 إحصائيات سريعة:\n• جاري التحميل..."

    def show_section(self, section):
        """عرض قسم معين"""
        # تحديث الزر النشط
        if self.active_menu_button:
            self.active_menu_button.config(bg=self.colors['dark'])

        self.active_menu_button = self.menu_buttons[section]
        self.active_menu_button.config(bg=self.colors['secondary'])

        # مسح المحتوى الحالي
        if self.current_content:
            self.current_content.destroy()

        # عرض القسم المطلوب
        if section == "dashboard":
            self.show_dashboard()
        elif section == "projects":
            self.show_projects()
        elif section == "tasks":
            self.show_tasks()
        elif section == "users":
            self.show_users()
        elif section == "reports":
            self.show_reports()
        elif section == "files":
            self.show_files()
        elif section == "comments":
            self.show_comments()
        elif section == "analytics":
            self.show_analytics()
        elif section == "settings":
            self.show_settings()

    def show_notifications(self):
        """عرض الإشعارات"""
        messagebox.showinfo("الإشعارات", "لديك 3 إشعارات جديدة:\n• مشروع جديد تم إضافته\n• مهمة تحتاج موافقة\n• تقرير شهري جاهز")

    def quick_search(self):
        """البحث السريع"""
        search_window = tk.Toplevel(self.main_window)
        search_window.title("البحث السريع")
        search_window.geometry("400x300")
        search_window.transient(self.main_window)
        search_window.grab_set()

        tk.Label(search_window, text="🔍 البحث السريع", font=self.fonts['heading']).pack(pady=20)

        search_entry = tk.Entry(search_window, font=self.fonts['body'], width=40)
        search_entry.pack(pady=10)
        search_entry.focus()

        tk.Button(search_window, text="بحث", font=self.fonts['button'],
                 command=lambda: messagebox.showinfo("نتائج البحث", "لم يتم العثور على نتائج")).pack(pady=10)

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        if result:
            # تسجيل النشاط
            self.log_activity(self.current_user['id'], "تسجيل خروج", "users",
                             self.current_user['id'], f"تسجيل خروج المستخدم {self.current_user['full_name']}")

            self.main_window.destroy()
            self.current_user = None
            self.create_login_window()
            self.login_window.mainloop()

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.main_window.bind('<Control-d>', lambda e: self.show_section('dashboard'))
        self.main_window.bind('<Control-p>', lambda e: self.show_section('projects'))
        self.main_window.bind('<Control-t>', lambda e: self.show_section('tasks'))
        self.main_window.bind('<Control-u>', lambda e: self.show_section('users'))
        self.main_window.bind('<Control-r>', lambda e: self.show_section('reports'))
        self.main_window.bind('<Control-q>', lambda e: self.logout())
        self.main_window.bind('<F1>', lambda e: self.show_help())
        self.main_window.bind('<F5>', lambda e: self.refresh_current_view())

    def show_help(self):
        """عرض المساعدة"""
        help_text = """🔧 اختصارات لوحة المفاتيح:

Ctrl+D - لوحة التحكم
Ctrl+P - إدارة المشاريع
Ctrl+T - إدارة المهام
Ctrl+U - إدارة المستخدمين
Ctrl+R - التقارير
Ctrl+Q - تسجيل الخروج
F1 - المساعدة
F5 - تحديث"""

        messagebox.showinfo("المساعدة", help_text)

    def refresh_current_view(self):
        """تحديث العرض الحالي"""
        # تحديث الإحصائيات في الشريط الجانبي
        for widget in self.sidebar.winfo_children():
            if isinstance(widget, tk.Frame):
                for child in widget.winfo_children():
                    if isinstance(child, tk.Label) and "إحصائيات سريعة" in child.cget("text"):
                        child.config(text=self.get_quick_stats())
                        break

    def run(self):
        """تشغيل التطبيق"""
        self.splash.mainloop()

    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.current_content = tk.Frame(self.content_area, bg=self.colors['light'])
        self.current_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان لوحة التحكم
        header_frame = tk.Frame(self.current_content, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="🏠 لوحة التحكم",
                              font=self.fonts['title'], bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)

        # التاريخ والوقت
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        time_label = tk.Label(header_frame, text=f"📅 {current_time}",
                             font=self.fonts['body'], bg=self.colors['white'],
                             fg=self.colors['dark'])
        time_label.pack(side=tk.RIGHT, pady=20, padx=20)

        # بطاقات الإحصائيات
        stats_frame = tk.Frame(self.current_content, bg=self.colors['light'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        # الحصول على الإحصائيات
        stats = self.get_dashboard_stats()

        # إنشاء البطاقات
        cards_data = [
            ("إجمالي المشاريع", stats['total_projects'], "🏗️", self.colors['primary']),
            ("المشاريع النشطة", stats['active_projects'], "⚡", self.colors['success']),
            ("المشاريع المكتملة", stats['completed_projects'], "✅", self.colors['info']),
            ("إجمالي الميزانية", f"{stats['total_budget']:,.0f} ريال", "💰", self.colors['warning'])
        ]

        for i, (title, value, icon, color) in enumerate(cards_data):
            card = self.create_stat_card(stats_frame, title, value, icon, color)
            card.grid(row=0, column=i, padx=10, sticky="ew")
            stats_frame.grid_columnconfigure(i, weight=1)

        # المحتوى الرئيسي
        main_content_frame = tk.Frame(self.current_content, bg=self.colors['light'])
        main_content_frame.pack(fill=tk.BOTH, expand=True)

        # الجانب الأيسر - المشاريع الحديثة
        left_panel = tk.Frame(main_content_frame, bg=self.colors['white'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        self.create_recent_projects_panel(left_panel)

        # الجانب الأيمن - الأنشطة والإشعارات
        right_panel = tk.Frame(main_content_frame, bg=self.colors['white'], width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_panel.pack_propagate(False)

        self.create_activity_panel(right_panel)

    def create_stat_card(self, parent, title, value, icon, color):
        """إنشاء بطاقة إحصائية"""
        card = tk.Frame(parent, bg=self.colors['white'], relief=tk.RAISED, bd=1)

        # الأيقونة والقيمة
        top_frame = tk.Frame(card, bg=color, height=60)
        top_frame.pack(fill=tk.X)
        top_frame.pack_propagate(False)

        icon_label = tk.Label(top_frame, text=icon, font=('Arial', 24),
                             bg=color, fg=self.colors['white'])
        icon_label.pack(side=tk.LEFT, pady=15, padx=20)

        value_label = tk.Label(top_frame, text=str(value), font=self.fonts['heading'],
                              bg=color, fg=self.colors['white'])
        value_label.pack(side=tk.RIGHT, pady=15, padx=20)

        # العنوان
        title_label = tk.Label(card, text=title, font=self.fonts['body'],
                              bg=self.colors['white'], fg=self.colors['dark'])
        title_label.pack(pady=15)

        return card

    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة التحكم"""
        stats = {}

        try:
            # إجمالي المشاريع
            self.cursor.execute("SELECT COUNT(*) FROM projects")
            stats['total_projects'] = self.cursor.fetchone()[0]

            # المشاريع النشطة
            self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'قيد التنفيذ'")
            stats['active_projects'] = self.cursor.fetchone()[0]

            # المشاريع المكتملة
            self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'مكتمل'")
            stats['completed_projects'] = self.cursor.fetchone()[0]

            # إجمالي الميزانية
            self.cursor.execute("SELECT SUM(planned_budget) FROM projects")
            result = self.cursor.fetchone()[0]
            stats['total_budget'] = result if result else 0

        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            stats = {'total_projects': 0, 'active_projects': 0, 'completed_projects': 0, 'total_budget': 0}

        return stats

    def create_recent_projects_panel(self, parent):
        """إنشاء لوحة المشاريع الحديثة"""
        # العنوان
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="🏗️ المشاريع الحديثة",
                              font=self.fonts['subheading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(side=tk.LEFT, pady=12, padx=20)

        # زر عرض الكل
        view_all_btn = tk.Button(header_frame, text="عرض الكل →",
                                font=self.fonts['small'], bg=self.colors['secondary'],
                                fg=self.colors['white'], relief=tk.FLAT,
                                command=lambda: self.show_section('projects'), cursor="hand2")
        view_all_btn.pack(side=tk.RIGHT, pady=12, padx=20)

        # قائمة المشاريع
        projects_frame = tk.Frame(parent, bg=self.colors['white'])
        projects_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الحصول على المشاريع الحديثة
        try:
            self.cursor.execute('''
                SELECT name, status, completion_percentage, planned_budget
                FROM projects
                ORDER BY created_date DESC
                LIMIT 5
            ''')
            projects = self.cursor.fetchall()

            if projects:
                for i, project in enumerate(projects):
                    self.create_project_item(projects_frame, project, i)
            else:
                no_projects_label = tk.Label(projects_frame, text="لا توجد مشاريع حالياً",
                                           font=self.fonts['body'], bg=self.colors['white'],
                                           fg=self.colors['dark'])
                no_projects_label.pack(expand=True)
        except Exception as e:
            error_label = tk.Label(projects_frame, text=f"خطأ في تحميل المشاريع: {e}",
                                 font=self.fonts['body'], bg=self.colors['white'],
                                 fg=self.colors['danger'])
            error_label.pack(expand=True)

    def create_project_item(self, parent, project, index):
        """إنشاء عنصر مشروع"""
        item_frame = tk.Frame(parent, bg=self.colors['light'] if index % 2 == 0 else self.colors['white'])
        item_frame.pack(fill=tk.X, pady=2)

        # اسم المشروع
        name_label = tk.Label(item_frame, text=project[0][:40] + "..." if len(project[0]) > 40 else project[0],
                             font=self.fonts['body'], bg=item_frame.cget('bg'),
                             fg=self.colors['dark'])
        name_label.pack(anchor=tk.W, padx=15, pady=(10, 5))

        # معلومات إضافية
        info_frame = tk.Frame(item_frame, bg=item_frame.cget('bg'))
        info_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        # الحالة
        status_color = self.get_status_color(project[1])
        status_label = tk.Label(info_frame, text=f"● {project[1]}",
                               font=self.fonts['small'], bg=item_frame.cget('bg'),
                               fg=status_color)
        status_label.pack(side=tk.LEFT)

        # نسبة الإنجاز
        progress_label = tk.Label(info_frame, text=f"{project[2]:.1f}%",
                                 font=self.fonts['small'], bg=item_frame.cget('bg'),
                                 fg=self.colors['dark'])
        progress_label.pack(side=tk.RIGHT)

        # الميزانية
        budget_label = tk.Label(info_frame, text=f"{project[3]:,.0f} ريال",
                               font=self.fonts['small'], bg=item_frame.cget('bg'),
                               fg=self.colors['dark'])
        budget_label.pack(side=tk.RIGHT, padx=(0, 20))

    def get_status_color(self, status):
        """الحصول على لون الحالة"""
        colors = {
            'مخطط': self.colors['info'],
            'قيد التنفيذ': self.colors['warning'],
            'مكتمل': self.colors['success'],
            'متوقف': self.colors['danger'],
            'ملغي': self.colors['dark']
        }
        return colors.get(status, self.colors['dark'])

    def create_activity_panel(self, parent):
        """إنشاء لوحة الأنشطة"""
        # العنوان
        header_frame = tk.Frame(parent, bg=self.colors['accent'], height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="📊 الأنشطة الحديثة",
                              font=self.fonts['subheading'], bg=self.colors['accent'],
                              fg=self.colors['white'])
        title_label.pack(pady=12, padx=20)

        # قائمة الأنشطة
        activities_frame = tk.Frame(parent, bg=self.colors['white'])
        activities_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # أنشطة تجريبية
        activities = [
            ("تم إنشاء مشروع جديد", "منذ ساعتين", "🆕"),
            ("تم تحديث مهمة", "منذ 4 ساعات", "✏️"),
            ("تم رفع ملف جديد", "أمس", "📁"),
            ("تم إنجاز مهمة", "أمس", "✅"),
            ("تم إضافة تعليق", "منذ يومين", "💬")
        ]

        for i, (activity, time, icon) in enumerate(activities):
            activity_frame = tk.Frame(activities_frame, bg=self.colors['light'] if i % 2 == 0 else self.colors['white'])
            activity_frame.pack(fill=tk.X, pady=2)

            icon_label = tk.Label(activity_frame, text=icon, font=('Arial', 16),
                                 bg=activity_frame.cget('bg'))
            icon_label.pack(side=tk.LEFT, padx=(10, 15), pady=10)

            text_frame = tk.Frame(activity_frame, bg=activity_frame.cget('bg'))
            text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=10)

            activity_label = tk.Label(text_frame, text=activity,
                                     font=self.fonts['small'], bg=activity_frame.cget('bg'),
                                     fg=self.colors['dark'])
            activity_label.pack(anchor=tk.W)

            time_label = tk.Label(text_frame, text=time,
                                 font=self.fonts['small'], bg=activity_frame.cget('bg'),
                                 fg=self.colors['dark'])
            time_label.pack(anchor=tk.W)

    def show_projects(self):
        """عرض إدارة المشاريع"""
        messagebox.showinfo("قريباً", "وحدة إدارة المشاريع قيد التطوير")

    def show_tasks(self):
        """عرض إدارة المهام"""
        messagebox.showinfo("قريباً", "وحدة إدارة المهام قيد التطوير")

    def show_users(self):
        """عرض إدارة المستخدمين"""
        messagebox.showinfo("قريباً", "وحدة إدارة المستخدمين قيد التطوير")

    def show_reports(self):
        """عرض التقارير"""
        messagebox.showinfo("قريباً", "وحدة التقارير قيد التطوير")

    def show_files(self):
        """عرض إدارة الملفات"""
        messagebox.showinfo("قريباً", "وحدة إدارة الملفات قيد التطوير")

    def show_comments(self):
        """عرض التعليقات"""
        messagebox.showinfo("قريباً", "وحدة التعليقات قيد التطوير")

    def show_analytics(self):
        """عرض تحليل الأداء"""
        messagebox.showinfo("قريباً", "وحدة تحليل الأداء قيد التطوير")

    def show_settings(self):
        """عرض الإعدادات"""
        messagebox.showinfo("قريباً", "وحدة الإعدادات قيد التطوير")

    def __del__(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()

def main():
    """الدالة الرئيسية"""
    try:
        app = ModernProjectManager()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
