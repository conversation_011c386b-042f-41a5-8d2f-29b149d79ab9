#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نسخة مبسطة من نظام إدارة المشاريع الهندسية - بلدية كفرنجة
Simplified Engineering Project Management System - Kufranja Municipality

هذه النسخة تعمل مع tkinter الأساسي فقط بدون مكتبات إضافية
This version works with basic tkinter only without additional libraries
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
import hashlib
from datetime import datetime
import os
from pathlib import Path

class SimpleProjectManager:
    """نظام إدارة المشاريع المبسط"""
    
    def __init__(self):
        """تهيئة النظام"""
        self.setup_database()
        self.current_user = None
        self.create_login_window()
    
    def setup_database(self):
        """إعداد قاعدة البيانات SQLite"""
        # إنشاء مجلد قاعدة البيانات
        Path("database").mkdir(exist_ok=True)
        
        # الاتصال بقاعدة البيانات
        self.db_path = "database/kufranja_simple.db"
        self.conn = sqlite3.connect(self.db_path)
        self.cursor = self.conn.cursor()
        
        # إنشاء الجداول
        self.create_tables()
        self.insert_default_data()
    
    def create_tables(self):
        """إنشاء الجداول الأساسية"""
        
        # جدول المستخدمين
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                role TEXT NOT NULL,
                is_active INTEGER DEFAULT 1,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول المشاريع
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                location TEXT,
                start_date DATE,
                end_date DATE,
                budget REAL,
                status TEXT DEFAULT 'مخطط',
                completion_percentage REAL DEFAULT 0,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # جدول المهام
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                assigned_to TEXT,
                start_date DATE,
                end_date DATE,
                status TEXT DEFAULT 'غير مبدوء',
                completion_percentage REAL DEFAULT 0,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (project_id) REFERENCES projects (id)
            )
        ''')
        
        self.conn.commit()
    
    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        # التحقق من وجود المستخدم الافتراضي
        self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if self.cursor.fetchone()[0] == 0:
            # إضافة المستخدم الافتراضي
            hashed_password = hashlib.sha256("admin123".encode()).hexdigest()
            self.cursor.execute('''
                INSERT INTO users (username, password, full_name, role)
                VALUES (?, ?, ?, ?)
            ''', ("admin", hashed_password, "مدير النظام", "Admin"))
            
            # إضافة مشروع تجريبي
            self.cursor.execute('''
                INSERT INTO projects (name, description, location, budget, created_by)
                VALUES (?, ?, ?, ?, ?)
            ''', ("مشروع تطوير الطرق", "تطوير وصيانة الطرق الرئيسية في البلدية", "كفرنجة", 500000, 1))
            
            self.conn.commit()
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        hashed_password = self.hash_password(password)
        self.cursor.execute('''
            SELECT id, username, full_name, role 
            FROM users 
            WHERE username = ? AND password = ? AND is_active = 1
        ''', (username, hashed_password))
        
        result = self.cursor.fetchone()
        if result:
            return {
                'id': result[0],
                'username': result[1],
                'full_name': result[2],
                'role': result[3]
            }
        return None
    
    def create_login_window(self):
        """إنشاء نافذة تسجيل الدخول"""
        self.login_window = tk.Tk()
        self.login_window.title("تسجيل الدخول - نظام إدارة المشاريع الهندسية")
        self.login_window.geometry("400x300")
        self.login_window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window(self.login_window, 400, 300)
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.login_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_label = ttk.Label(main_frame, text="نظام إدارة المشاريع الهندسية", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 10))
        
        subtitle_label = ttk.Label(main_frame, text="بلدية كفرنجة الجديدة", 
                                  font=("Arial", 12))
        subtitle_label.pack(pady=(0, 20))
        
        # حقل اسم المستخدم
        ttk.Label(main_frame, text="اسم المستخدم:").pack(anchor=tk.W, pady=(0, 5))
        self.username_entry = ttk.Entry(main_frame, width=30, font=("Arial", 10))
        self.username_entry.pack(pady=(0, 10))
        
        # حقل كلمة المرور
        ttk.Label(main_frame, text="كلمة المرور:").pack(anchor=tk.W, pady=(0, 5))
        self.password_entry = ttk.Entry(main_frame, width=30, show="*", font=("Arial", 10))
        self.password_entry.pack(pady=(0, 20))
        
        # زر تسجيل الدخول
        login_button = ttk.Button(main_frame, text="تسجيل الدخول", 
                                 command=self.login, width=20)
        login_button.pack(pady=(0, 10))
        
        # معلومات المستخدم الافتراضي
        info_label = ttk.Label(main_frame, text="المستخدم الافتراضي: admin | كلمة المرور: admin123", 
                              font=("Arial", 9), foreground="gray")
        info_label.pack(pady=(10, 0))
        
        # ربط مفتاح Enter
        self.login_window.bind('<Return>', lambda e: self.login())
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def center_window(self, window, width, height):
        """توسيط النافذة على الشاشة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    def login(self):
        """معالج تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return
        
        user = self.authenticate_user(username, password)
        if user:
            self.current_user = user
            messagebox.showinfo("نجح تسجيل الدخول", f"مرحباً {user['full_name']}")
            self.login_window.destroy()
            self.create_main_window()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)
    
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.main_window = tk.Tk()
        self.main_window.title(f"نظام إدارة المشاريع - {self.current_user['full_name']}")
        self.main_window.geometry("1000x700")
        self.main_window.state('zoomed')
        
        # إنشاء القوائم
        self.create_menu()
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.main_window)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # العنوان
        title_frame = ttk.Frame(main_frame)
        title_frame.pack(fill=tk.X, pady=(0, 20))
        
        title_label = ttk.Label(title_frame, text="نظام إدارة المشاريع الهندسية - بلدية كفرنجة", 
                               font=("Arial", 18, "bold"))
        title_label.pack(side=tk.LEFT)
        
        user_label = ttk.Label(title_frame, text=f"مرحباً، {self.current_user['full_name']}", 
                              font=("Arial", 12))
        user_label.pack(side=tk.RIGHT)
        
        # إنشاء التبويبات
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # تبويب لوحة التحكم
        self.create_dashboard_tab()
        
        # تبويب المشاريع
        self.create_projects_tab()
        
        # تبويب المهام
        self.create_tasks_tab()
    
    def create_menu(self):
        """إنشاء شريط القوائم"""
        menubar = tk.Menu(self.main_window)
        self.main_window.config(menu=menubar)
        
        # قائمة الملف
        file_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="ملف", menu=file_menu)
        file_menu.add_command(label="تسجيل الخروج", command=self.logout)
        file_menu.add_separator()
        file_menu.add_command(label="خروج", command=self.main_window.quit)
        
        # قائمة المشاريع
        projects_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="المشاريع", menu=projects_menu)
        projects_menu.add_command(label="مشروع جديد", command=self.add_project)
        projects_menu.add_command(label="عرض المشاريع", command=lambda: self.notebook.select(1))
        
        # قائمة المساعدة
        help_menu = tk.Menu(menubar, tearoff=0)
        menubar.add_cascade(label="مساعدة", menu=help_menu)
        help_menu.add_command(label="حول البرنامج", command=self.show_about)
    
    def create_dashboard_tab(self):
        """إنشاء تبويب لوحة التحكم"""
        dashboard_frame = ttk.Frame(self.notebook)
        self.notebook.add(dashboard_frame, text="لوحة التحكم")
        
        # إحصائيات سريعة
        stats_frame = ttk.LabelFrame(dashboard_frame, text="إحصائيات سريعة", padding="10")
        stats_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # الحصول على الإحصائيات
        self.cursor.execute("SELECT COUNT(*) FROM projects")
        total_projects = self.cursor.fetchone()[0]
        
        self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'قيد التنفيذ'")
        active_projects = self.cursor.fetchone()[0]
        
        self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'مكتمل'")
        completed_projects = self.cursor.fetchone()[0]
        
        self.cursor.execute("SELECT SUM(budget) FROM projects")
        total_budget = self.cursor.fetchone()[0] or 0
        
        # عرض الإحصائيات
        stats_text = f"""
إجمالي المشاريع: {total_projects}
المشاريع النشطة: {active_projects}
المشاريع المكتملة: {completed_projects}
إجمالي الميزانية: {total_budget:,.0f} ريال
        """
        
        stats_label = ttk.Label(stats_frame, text=stats_text, font=("Arial", 12))
        stats_label.pack()
        
        # المشاريع الحديثة
        recent_frame = ttk.LabelFrame(dashboard_frame, text="المشاريع الحديثة", padding="10")
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # جدول المشاريع الحديثة
        columns = ("ID", "اسم المشروع", "الحالة", "الميزانية")
        recent_tree = ttk.Treeview(recent_frame, columns=columns, show="headings", height=8)
        
        for col in columns:
            recent_tree.heading(col, text=col)
            recent_tree.column(col, width=150, anchor=tk.CENTER)
        
        # إضافة البيانات
        self.cursor.execute('''
            SELECT id, name, status, budget 
            FROM projects 
            ORDER BY created_date DESC 
            LIMIT 10
        ''')
        
        for project in self.cursor.fetchall():
            recent_tree.insert("", tk.END, values=(
                project[0],
                project[1],
                project[2],
                f"{project[3]:,.0f}" if project[3] else "0"
            ))
        
        recent_tree.pack(fill=tk.BOTH, expand=True)
    
    def create_projects_tab(self):
        """إنشاء تبويب المشاريع"""
        projects_frame = ttk.Frame(self.notebook)
        self.notebook.add(projects_frame, text="المشاريع")
        
        # شريط الأدوات
        toolbar_frame = ttk.Frame(projects_frame)
        toolbar_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(toolbar_frame, text="➕ مشروع جديد", 
                  command=self.add_project).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="✏️ تعديل", 
                  command=self.edit_project).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="🗑️ حذف", 
                  command=self.delete_project).pack(side=tk.LEFT, padx=5)
        ttk.Button(toolbar_frame, text="🔄 تحديث", 
                  command=self.refresh_projects).pack(side=tk.LEFT, padx=5)
        
        # جدول المشاريع
        columns = ("ID", "اسم المشروع", "الموقع", "تاريخ البداية", "تاريخ النهاية", "الميزانية", "الحالة", "نسبة الإنجاز")
        self.projects_tree = ttk.Treeview(projects_frame, columns=columns, show="headings")
        
        for col in columns:
            self.projects_tree.heading(col, text=col)
            self.projects_tree.column(col, width=120, anchor=tk.CENTER)
        
        # شريط التمرير
        scrollbar = ttk.Scrollbar(projects_frame, orient=tk.VERTICAL, command=self.projects_tree.yview)
        self.projects_tree.configure(yscrollcommand=scrollbar.set)
        
        self.projects_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 10), pady=10)
        
        # تحميل البيانات
        self.refresh_projects()
    
    def create_tasks_tab(self):
        """إنشاء تبويب المهام"""
        tasks_frame = ttk.Frame(self.notebook)
        self.notebook.add(tasks_frame, text="المهام")
        
        # محتوى مؤقت
        ttk.Label(tasks_frame, text="تبويب المهام قيد التطوير", 
                 font=("Arial", 16)).pack(expand=True)
    
    def refresh_projects(self):
        """تحديث قائمة المشاريع"""
        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)
        
        # تحميل البيانات الجديدة
        self.cursor.execute('''
            SELECT id, name, location, start_date, end_date, budget, status, completion_percentage
            FROM projects
            ORDER BY created_date DESC
        ''')
        
        for project in self.cursor.fetchall():
            self.projects_tree.insert("", tk.END, values=(
                project[0],
                project[1],
                project[2] or "",
                project[3] or "",
                project[4] or "",
                f"{project[5]:,.0f}" if project[5] else "0",
                project[6],
                f"{project[7]:.1f}%" if project[7] else "0%"
            ))
    
    def add_project(self):
        """إضافة مشروع جديد"""
        self.project_form(mode="add")
    
    def edit_project(self):
        """تعديل مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        self.project_form(mode="edit", project_id=project_id)
    
    def delete_project(self):
        """حذف مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]
        
        result = messagebox.askyesno("تأكيد الحذف", 
                                   f"هل أنت متأكد من حذف المشروع '{project_name}'؟")
        
        if result:
            self.cursor.execute("DELETE FROM projects WHERE id = ?", (project_id,))
            self.conn.commit()
            messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
            self.refresh_projects()
    
    def project_form(self, mode="add", project_id=None):
        """نموذج إضافة/تعديل مشروع"""
        form_window = tk.Toplevel(self.main_window)
        title = "إضافة مشروع جديد" if mode == "add" else "تعديل مشروع"
        form_window.title(title)
        form_window.geometry("500x400")
        form_window.resizable(False, False)
        form_window.transient(self.main_window)
        form_window.grab_set()
        
        # توسيط النافذة
        self.center_window(form_window, 500, 400)
        
        # الإطار الرئيسي
        main_frame = ttk.Frame(form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        ttk.Label(main_frame, text=title, font=("Arial", 16, "bold")).pack(pady=(0, 20))
        
        # الحقول
        ttk.Label(main_frame, text="اسم المشروع *:").pack(anchor=tk.W)
        name_entry = ttk.Entry(main_frame, width=50)
        name_entry.pack(pady=(5, 10))
        
        ttk.Label(main_frame, text="الوصف:").pack(anchor=tk.W)
        desc_text = tk.Text(main_frame, height=4, width=50)
        desc_text.pack(pady=(5, 10))
        
        ttk.Label(main_frame, text="الموقع:").pack(anchor=tk.W)
        location_entry = ttk.Entry(main_frame, width=50)
        location_entry.pack(pady=(5, 10))
        
        ttk.Label(main_frame, text="الميزانية (ريال):").pack(anchor=tk.W)
        budget_entry = ttk.Entry(main_frame, width=50)
        budget_entry.pack(pady=(5, 10))
        
        ttk.Label(main_frame, text="الحالة:").pack(anchor=tk.W)
        status_combo = ttk.Combobox(main_frame, values=["مخطط", "قيد التنفيذ", "متوقف", "مكتمل", "ملغي"], 
                                   state="readonly", width=47)
        status_combo.set("مخطط")
        status_combo.pack(pady=(5, 20))
        
        # تحميل البيانات في حالة التعديل
        if mode == "edit" and project_id:
            self.cursor.execute('''
                SELECT name, description, location, budget, status
                FROM projects WHERE id = ?
            ''', (project_id,))
            
            project = self.cursor.fetchone()
            if project:
                name_entry.insert(0, project[0])
                desc_text.insert("1.0", project[1] or "")
                location_entry.insert(0, project[2] or "")
                budget_entry.insert(0, str(project[3] or ""))
                status_combo.set(project[4])
        
        # الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(fill=tk.X)
        
        def save_project():
            name = name_entry.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
                return
            
            description = desc_text.get("1.0", tk.END).strip()
            location = location_entry.get().strip()
            try:
                budget = float(budget_entry.get()) if budget_entry.get() else 0
            except ValueError:
                budget = 0
            status = status_combo.get()
            
            if mode == "add":
                self.cursor.execute('''
                    INSERT INTO projects (name, description, location, budget, status, created_by)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (name, description, location, budget, status, self.current_user['id']))
                message = "تم إضافة المشروع بنجاح"
            else:
                self.cursor.execute('''
                    UPDATE projects 
                    SET name = ?, description = ?, location = ?, budget = ?, status = ?
                    WHERE id = ?
                ''', (name, description, location, budget, status, project_id))
                message = "تم تحديث المشروع بنجاح"
            
            self.conn.commit()
            messagebox.showinfo("نجح", message)
            form_window.destroy()
            self.refresh_projects()
        
        ttk.Button(buttons_frame, text="💾 حفظ", command=save_project).pack(side=tk.RIGHT, padx=5)
        ttk.Button(buttons_frame, text="❌ إلغاء", command=form_window.destroy).pack(side=tk.RIGHT)
        
        # تركيز على حقل الاسم
        name_entry.focus()
    
    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        if result:
            self.main_window.destroy()
            self.current_user = None
            self.create_login_window()
            self.login_window.mainloop()
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        about_text = """
نظام إدارة المشاريع الهندسية
بلدية كفرنجة الجديدة

الإصدار: 1.0.0 (مبسط)
تاريخ الإصدار: ديسمبر 2024

تطوير: فريق تقنية المعلومات
        """
        messagebox.showinfo("حول البرنامج", about_text)
    
    def run(self):
        """تشغيل التطبيق"""
        self.login_window.mainloop()
    
    def __del__(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()

def main():
    """الدالة الرئيسية"""
    try:
        app = SimpleProjectManager()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
