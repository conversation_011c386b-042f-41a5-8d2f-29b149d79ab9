#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المهام المتقدمة
Advanced Tasks Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime, timedelta
import calendar

class AdvancedTasksManager:
    """مدير المهام المتقدم"""
    
    def __init__(self, parent, db_manager, current_user, colors, fonts):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        self.tasks_tree = None
        self.search_var = tk.StringVar()
        self.filter_project_var = tk.StringVar(value="جميع المشاريع")
        self.filter_status_var = tk.StringVar(value="جميع الحالات")
        self.filter_priority_var = tk.StringVar(value="جميع الأولويات")
        self.view_mode = tk.StringVar(value="list")  # list, kanban, calendar
        
    def create_tasks_interface(self, content_area):
        """إنشاء واجهة إدارة المهام"""
        # مسح المحتوى السابق
        for widget in content_area.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(content_area, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان والأدوات
        self.create_header(main_frame)
        
        # شريط البحث والفلاتر
        self.create_filters_bar(main_frame)
        
        # شريط أوضاع العرض
        self.create_view_modes_bar(main_frame)
        
        # منطقة المحتوى الرئيسية
        self.content_frame = tk.Frame(main_frame, bg=self.colors['light'])
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # عرض المهام حسب الوضع المحدد
        self.switch_view_mode()
        
    def create_header(self, parent):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(parent, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(header_frame, text="📋 إدارة المهام", 
                              font=self.fonts['title'], bg=self.colors['white'], 
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)
        
        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)
        
        # زر مهمة جديدة
        new_btn = tk.Button(tools_frame, text="➕ مهمة جديدة", 
                           font=self.fonts['button'], bg=self.colors['success'], 
                           fg=self.colors['white'], relief=tk.FLAT, 
                           command=self.add_task, cursor="hand2")
        new_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تصدير
        export_btn = tk.Button(tools_frame, text="📤 تصدير", 
                              font=self.fonts['button'], bg=self.colors['info'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.export_tasks, cursor="hand2")
        export_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تحديث
        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث", 
                               font=self.fonts['button'], bg=self.colors['secondary'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.load_tasks, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
    
    def create_filters_bar(self, parent):
        """إنشاء شريط الفلاتر"""
        filters_frame = tk.Frame(parent, bg=self.colors['white'], height=60)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        filters_frame.pack_propagate(False)
        
        # البحث
        search_label = tk.Label(filters_frame, text="🔍 البحث:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        search_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        search_entry = tk.Entry(filters_frame, textvariable=self.search_var, 
                               font=self.fonts['body'], width=20, 
                               bg=self.colors['light'])
        search_entry.pack(side=tk.LEFT, padx=5, pady=15, ipady=5)
        search_entry.bind('<KeyRelease>', self.search_tasks)
        
        # فلتر المشروع
        project_label = tk.Label(filters_frame, text="🏗️ المشروع:", 
                                font=self.fonts['body'], bg=self.colors['white'])
        project_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        projects = self.get_projects_list()
        project_combo = ttk.Combobox(filters_frame, textvariable=self.filter_project_var, 
                                    values=projects, state="readonly", width=15)
        project_combo.pack(side=tk.LEFT, padx=5, pady=15)
        project_combo.bind('<<ComboboxSelected>>', self.filter_tasks)
        
        # فلتر الحالة
        status_label = tk.Label(filters_frame, text="📊 الحالة:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        status_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        status_combo = ttk.Combobox(filters_frame, textvariable=self.filter_status_var, 
                                   values=["جميع الحالات", "غير مبدوء", "قيد التنفيذ", "مكتمل", "متوقف", "ملغي"], 
                                   state="readonly", width=12)
        status_combo.pack(side=tk.LEFT, padx=5, pady=15)
        status_combo.bind('<<ComboboxSelected>>', self.filter_tasks)
        
        # فلتر الأولوية
        priority_label = tk.Label(filters_frame, text="⚡ الأولوية:", 
                                 font=self.fonts['body'], bg=self.colors['white'])
        priority_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        priority_combo = ttk.Combobox(filters_frame, textvariable=self.filter_priority_var, 
                                     values=["جميع الأولويات", "منخفض", "متوسط", "عالي", "حرج"], 
                                     state="readonly", width=12)
        priority_combo.pack(side=tk.LEFT, padx=5, pady=15)
        priority_combo.bind('<<ComboboxSelected>>', self.filter_tasks)
    
    def create_view_modes_bar(self, parent):
        """إنشاء شريط أوضاع العرض"""
        modes_frame = tk.Frame(parent, bg=self.colors['dark'], height=50)
        modes_frame.pack(fill=tk.X, pady=(0, 10))
        modes_frame.pack_propagate(False)
        
        tk.Label(modes_frame, text="👁️ وضع العرض:", font=self.fonts['body'], 
                bg=self.colors['dark'], fg=self.colors['white']).pack(side=tk.LEFT, padx=20, pady=12)
        
        # أزرار أوضاع العرض
        modes = [
            ("📋 قائمة", "list"),
            ("📌 كانبان", "kanban"),
            ("📅 تقويم", "calendar"),
            ("📊 جانت", "gantt")
        ]
        
        for text, mode in modes:
            btn = tk.Button(modes_frame, text=text, font=self.fonts['small'], 
                           bg=self.colors['secondary'] if self.view_mode.get() == mode else self.colors['light'], 
                           fg=self.colors['white'] if self.view_mode.get() == mode else self.colors['dark'], 
                           relief=tk.FLAT, cursor="hand2",
                           command=lambda m=mode: self.set_view_mode(m))
            btn.pack(side=tk.LEFT, padx=5, pady=10, ipadx=10)
    
    def set_view_mode(self, mode):
        """تعيين وضع العرض"""
        self.view_mode.set(mode)
        self.switch_view_mode()
        # تحديث ألوان الأزرار
        self.create_view_modes_bar(self.content_frame.master)
    
    def switch_view_mode(self):
        """تبديل وضع العرض"""
        # مسح المحتوى الحالي
        for widget in self.content_frame.winfo_children():
            widget.destroy()
        
        mode = self.view_mode.get()
        if mode == "list":
            self.create_list_view()
        elif mode == "kanban":
            self.create_kanban_view()
        elif mode == "calendar":
            self.create_calendar_view()
        elif mode == "gantt":
            self.create_gantt_view()
    
    def create_list_view(self):
        """إنشاء عرض القائمة"""
        # جدول المهام
        columns = ("ID", "اسم المهمة", "المشروع", "المسؤول", "تاريخ البداية", "تاريخ النهاية", 
                  "الساعات المقدرة", "الساعات الفعلية", "الحالة", "الأولوية", "نسبة الإنجاز")
        
        self.tasks_tree = ttk.Treeview(self.content_frame, columns=columns, show="headings", height=20)
        
        # تعريف العناوين والعرض
        column_widths = {
            "ID": 50, "اسم المهمة": 200, "المشروع": 150, "المسؤول": 120,
            "تاريخ البداية": 100, "تاريخ النهاية": 100, "الساعات المقدرة": 100,
            "الساعات الفعلية": 100, "الحالة": 100, "الأولوية": 80, "نسبة الإنجاز": 100
        }
        
        for col in columns:
            self.tasks_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.tasks_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # شريط التمرير
        v_scrollbar = ttk.Scrollbar(self.content_frame, orient=tk.VERTICAL, command=self.tasks_tree.yview)
        h_scrollbar = ttk.Scrollbar(self.content_frame, orient=tk.HORIZONTAL, command=self.tasks_tree.xview)
        
        self.tasks_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.tasks_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        self.content_frame.grid_rowconfigure(0, weight=1)
        self.content_frame.grid_columnconfigure(0, weight=1)
        
        # قائمة السياق
        self.create_context_menu()
        
        # ربط الأحداث
        self.tasks_tree.bind('<Double-1>', self.edit_task)
        self.tasks_tree.bind('<Button-3>', self.show_context_menu)
        
        # تحميل البيانات
        self.load_tasks()
    
    def create_kanban_view(self):
        """إنشاء عرض كانبان"""
        # إطار كانبان
        kanban_frame = tk.Frame(self.content_frame, bg=self.colors['light'])
        kanban_frame.pack(fill=tk.BOTH, expand=True)
        
        # أعمدة كانبان
        statuses = ["غير مبدوء", "قيد التنفيذ", "مكتمل"]
        colors = [self.colors['info'], self.colors['warning'], self.colors['success']]
        
        for i, (status, color) in enumerate(zip(statuses, colors)):
            # عمود كانبان
            column_frame = tk.Frame(kanban_frame, bg=self.colors['white'], relief=tk.RAISED, bd=1)
            column_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=5, pady=5)
            
            # رأس العمود
            header_frame = tk.Frame(column_frame, bg=color, height=50)
            header_frame.pack(fill=tk.X)
            header_frame.pack_propagate(False)
            
            tk.Label(header_frame, text=f"📋 {status}", font=self.fonts['subheading'], 
                    bg=color, fg=self.colors['white']).pack(pady=12)
            
            # منطقة المهام
            tasks_frame = tk.Frame(column_frame, bg=self.colors['light'])
            tasks_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # تحميل مهام هذا العمود
            self.load_kanban_tasks(tasks_frame, status)
    
    def create_calendar_view(self):
        """إنشاء عرض التقويم"""
        # إطار التقويم
        calendar_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        calendar_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # شريط التنقل
        nav_frame = tk.Frame(calendar_frame, bg=self.colors['primary'], height=50)
        nav_frame.pack(fill=tk.X)
        nav_frame.pack_propagate(False)
        
        # التاريخ الحالي
        self.current_date = datetime.now()
        
        # أزرار التنقل
        prev_btn = tk.Button(nav_frame, text="◀", font=self.fonts['heading'], 
                           bg=self.colors['secondary'], fg=self.colors['white'], 
                           relief=tk.FLAT, command=self.prev_month, cursor="hand2")
        prev_btn.pack(side=tk.LEFT, padx=10, pady=10)
        
        self.month_label = tk.Label(nav_frame, text="", font=self.fonts['heading'], 
                                   bg=self.colors['primary'], fg=self.colors['white'])
        self.month_label.pack(side=tk.LEFT, expand=True)
        
        next_btn = tk.Button(nav_frame, text="▶", font=self.fonts['heading'], 
                           bg=self.colors['secondary'], fg=self.colors['white'], 
                           relief=tk.FLAT, command=self.next_month, cursor="hand2")
        next_btn.pack(side=tk.RIGHT, padx=10, pady=10)
        
        # شبكة التقويم
        self.calendar_grid = tk.Frame(calendar_frame, bg=self.colors['white'])
        self.calendar_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # إنشاء التقويم
        self.create_calendar_grid()
    
    def create_gantt_view(self):
        """إنشاء عرض جانت"""
        # إطار جانت
        gantt_frame = tk.Frame(self.content_frame, bg=self.colors['white'])
        gantt_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # رسالة مؤقتة
        tk.Label(gantt_frame, text="📊 مخطط جانت", font=self.fonts['title'], 
                bg=self.colors['white'], fg=self.colors['primary']).pack(pady=50)
        
        tk.Label(gantt_frame, text="عرض زمني تفاعلي للمهام والمشاريع\n(قيد التطوير)", 
                font=self.fonts['body'], bg=self.colors['white'], 
                fg=self.colors['dark']).pack()
        
        # إطار مبسط لعرض المهام
        simple_gantt = tk.Frame(gantt_frame, bg=self.colors['light'], relief=tk.RAISED, bd=1)
        simple_gantt.pack(fill=tk.BOTH, expand=True, padx=50, pady=50)
        
        # عرض مبسط للمهام
        try:
            tasks = self.db_manager.cursor.execute('''
                SELECT t.name, t.start_date, t.end_date, t.status, p.name as project_name
                FROM tasks t
                LEFT JOIN projects p ON t.project_id = p.id
                WHERE t.start_date IS NOT NULL AND t.end_date IS NOT NULL
                ORDER BY t.start_date
                LIMIT 10
            ''').fetchall()
            
            for i, task in enumerate(tasks):
                task_frame = tk.Frame(simple_gantt, bg=self.colors['white'], relief=tk.RAISED, bd=1)
                task_frame.pack(fill=tk.X, padx=10, pady=5)
                
                # معلومات المهمة
                info_text = f"📋 {task[0]} | 🏗️ {task[4]} | 📅 {task[1]} - {task[2]} | 📊 {task[3]}"
                tk.Label(task_frame, text=info_text, font=self.fonts['small'], 
                        bg=self.colors['white'], fg=self.colors['dark']).pack(pady=5, padx=10)
                
                # شريط تقدم مبسط
                progress_frame = tk.Frame(task_frame, bg=self.colors['light'], height=10)
                progress_frame.pack(fill=tk.X, padx=10, pady=(0, 5))
                
        except Exception as e:
            tk.Label(simple_gantt, text=f"خطأ في تحميل البيانات: {str(e)}",
                    font=self.fonts['body'], bg=self.colors['light'],
                    fg=self.colors['danger']).pack(pady=20)

    def get_projects_list(self):
        """جلب قائمة المشاريع"""
        try:
            projects = self.db_manager.cursor.execute('''
                SELECT name FROM projects ORDER BY name
            ''').fetchall()
            project_names = ["جميع المشاريع"] + [project[0] for project in projects]
            return project_names
        except:
            return ["جميع المشاريع"]

    def load_tasks(self):
        """تحميل قائمة المهام"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        try:
            # مسح البيانات الحالية
            for item in self.tasks_tree.get_children():
                self.tasks_tree.delete(item)

            # استعلام المهام
            query = """
                SELECT t.id, t.name, p.name as project_name, u.full_name as assigned_name,
                       t.start_date, t.end_date, t.estimated_hours, t.actual_hours,
                       t.status, t.priority, t.completion_percentage
                FROM tasks t
                LEFT JOIN projects p ON t.project_id = p.id
                LEFT JOIN users u ON t.assigned_to = u.id
                ORDER BY t.created_date DESC
            """

            tasks = self.db_manager.cursor.execute(query).fetchall()

            # إضافة البيانات للجدول
            for task in tasks:
                # تنسيق البيانات
                task_id = task[0]
                name = task[1] if task[1] else ""
                project = task[2] if task[2] else "غير محدد"
                assigned = task[3] if task[3] else "غير محدد"
                start_date = task[4] if task[4] else ""
                end_date = task[5] if task[5] else ""
                estimated_hours = f"{task[6]:.1f}" if task[6] else "0"
                actual_hours = f"{task[7]:.1f}" if task[7] else "0"
                status = task[8] if task[8] else "غير مبدوء"
                priority = task[9] if task[9] else "متوسط"
                completion = f"{task[10]:.1f}%" if task[10] else "0%"

                # إضافة الصف
                item = self.tasks_tree.insert("", tk.END, values=(
                    task_id, name, project, assigned, start_date, end_date,
                    estimated_hours, actual_hours, status, priority, completion
                ))

                # تلوين الصفوف حسب الحالة
                self.color_row_by_status(item, status)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المهام:\n{str(e)}")

    def load_kanban_tasks(self, parent_frame, status):
        """تحميل مهام عمود كانبان"""
        try:
            tasks = self.db_manager.cursor.execute('''
                SELECT t.id, t.name, p.name as project_name, t.priority, t.completion_percentage
                FROM tasks t
                LEFT JOIN projects p ON t.project_id = p.id
                WHERE t.status = ?
                ORDER BY t.priority DESC, t.created_date DESC
                LIMIT 10
            ''', (status,)).fetchall()

            for task in tasks:
                # بطاقة المهمة
                card_frame = tk.Frame(parent_frame, bg=self.colors['white'], relief=tk.RAISED, bd=1)
                card_frame.pack(fill=tk.X, pady=5)

                # اسم المهمة
                name_label = tk.Label(card_frame, text=task[1][:30] + "..." if len(task[1]) > 30 else task[1],
                                     font=self.fonts['body'], bg=self.colors['white'],
                                     fg=self.colors['dark'], wraplength=200)
                name_label.pack(anchor=tk.W, padx=10, pady=(10, 5))

                # معلومات إضافية
                info_frame = tk.Frame(card_frame, bg=self.colors['white'])
                info_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

                # المشروع
                project_label = tk.Label(info_frame, text=f"🏗️ {task[2] or 'غير محدد'}",
                                        font=self.fonts['small'], bg=self.colors['white'],
                                        fg=self.colors['dark'])
                project_label.pack(anchor=tk.W)

                # الأولوية ونسبة الإنجاز
                priority_color = self.get_priority_color(task[3])
                priority_label = tk.Label(info_frame, text=f"⚡ {task[3]}",
                                         font=self.fonts['small'], bg=self.colors['white'],
                                         fg=priority_color)
                priority_label.pack(side=tk.LEFT)

                completion_label = tk.Label(info_frame, text=f"{task[4]:.1f}%",
                                           font=self.fonts['small'], bg=self.colors['white'],
                                           fg=self.colors['dark'])
                completion_label.pack(side=tk.RIGHT)

                # ربط النقر
                card_frame.bind("<Button-1>", lambda e, tid=task[0]: self.edit_task_by_id(tid))

        except Exception as e:
            error_label = tk.Label(parent_frame, text=f"خطأ: {str(e)}",
                                 font=self.fonts['small'], bg=self.colors['light'],
                                 fg=self.colors['danger'])
            error_label.pack(pady=10)

    def create_calendar_grid(self):
        """إنشاء شبكة التقويم"""
        # مسح الشبكة الحالية
        for widget in self.calendar_grid.winfo_children():
            widget.destroy()

        # تحديث تسمية الشهر
        month_name = calendar.month_name[self.current_date.month]
        year = self.current_date.year
        self.month_label.config(text=f"{month_name} {year}")

        # أيام الأسبوع
        days = ["الأحد", "الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت"]
        for i, day in enumerate(days):
            day_label = tk.Label(self.calendar_grid, text=day, font=self.fonts['body'],
                               bg=self.colors['primary'], fg=self.colors['white'])
            day_label.grid(row=0, column=i, sticky="nsew", padx=1, pady=1)

        # أيام الشهر
        cal = calendar.monthcalendar(year, self.current_date.month)

        for week_num, week in enumerate(cal, 1):
            for day_num, day in enumerate(week):
                if day == 0:
                    # يوم فارغ
                    day_frame = tk.Frame(self.calendar_grid, bg=self.colors['light'])
                else:
                    # يوم صالح
                    day_frame = tk.Frame(self.calendar_grid, bg=self.colors['white'], relief=tk.RAISED, bd=1)

                    # رقم اليوم
                    day_label = tk.Label(day_frame, text=str(day), font=self.fonts['small'],
                                       bg=self.colors['white'], fg=self.colors['dark'])
                    day_label.pack(anchor=tk.NW, padx=2, pady=2)

                    # المهام في هذا اليوم (مبسط)
                    date_str = f"{year}-{self.current_date.month:02d}-{day:02d}"
                    tasks_count = self.get_tasks_count_for_date(date_str)

                    if tasks_count > 0:
                        tasks_label = tk.Label(day_frame, text=f"📋 {tasks_count}",
                                             font=self.fonts['small'], bg=self.colors['white'],
                                             fg=self.colors['secondary'])
                        tasks_label.pack(anchor=tk.SW, padx=2, pady=2)

                day_frame.grid(row=week_num, column=day_num, sticky="nsew", padx=1, pady=1)

        # تكوين الشبكة
        for i in range(7):
            self.calendar_grid.grid_columnconfigure(i, weight=1)
        for i in range(len(cal) + 1):
            self.calendar_grid.grid_rowconfigure(i, weight=1)

    def get_tasks_count_for_date(self, date_str):
        """الحصول على عدد المهام في تاريخ معين"""
        try:
            count = self.db_manager.cursor.execute('''
                SELECT COUNT(*) FROM tasks
                WHERE start_date <= ? AND end_date >= ?
            ''', (date_str, date_str)).fetchone()[0]
            return count
        except:
            return 0

    def prev_month(self):
        """الشهر السابق"""
        if self.current_date.month == 1:
            self.current_date = self.current_date.replace(year=self.current_date.year - 1, month=12)
        else:
            self.current_date = self.current_date.replace(month=self.current_date.month - 1)
        self.create_calendar_grid()

    def next_month(self):
        """الشهر التالي"""
        if self.current_date.month == 12:
            self.current_date = self.current_date.replace(year=self.current_date.year + 1, month=1)
        else:
            self.current_date = self.current_date.replace(month=self.current_date.month + 1)
        self.create_calendar_grid()

    def color_row_by_status(self, item, status):
        """تلوين الصف حسب الحالة"""
        colors = {
            'غير مبدوء': '#E3F2FD',      # أزرق فاتح
            'قيد التنفيذ': '#FFF3E0',    # برتقالي فاتح
            'مكتمل': '#E8F5E8',          # أخضر فاتح
            'متوقف': '#FFEBEE',          # أحمر فاتح
            'ملغي': '#F5F5F5'            # رمادي فاتح
        }

        color = colors.get(status, '#FFFFFF')
        self.tasks_tree.set(item, "الحالة", f"● {status}")

    def get_priority_color(self, priority):
        """الحصول على لون الأولوية"""
        colors = {
            'منخفض': self.colors['info'],
            'متوسط': self.colors['warning'],
            'عالي': self.colors['danger'],
            'حرج': '#8B0000'  # أحمر داكن
        }
        return colors.get(priority, self.colors['dark'])

    def search_tasks(self, event=None):
        """البحث في المهام"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        search_term = self.search_var.get().strip().lower()

        # إخفاء/إظهار الصفوف حسب البحث
        for item in self.tasks_tree.get_children():
            values = self.tasks_tree.item(item)['values']
            # البحث في اسم المهمة والمشروع والمسؤول
            if (search_term in str(values[1]).lower() or
                search_term in str(values[2]).lower() or
                search_term in str(values[3]).lower()):
                self.tasks_tree.reattach(item, '', tk.END)
            else:
                self.tasks_tree.detach(item)

    def filter_tasks(self, event=None):
        """فلترة المهام"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        project_filter = self.filter_project_var.get()
        status_filter = self.filter_status_var.get()
        priority_filter = self.filter_priority_var.get()

        for item in self.tasks_tree.get_children():
            values = self.tasks_tree.item(item)['values']
            show_item = True

            # فلتر المشروع
            if project_filter != "جميع المشاريع":
                if project_filter not in str(values[2]):
                    show_item = False

            # فلتر الحالة
            if status_filter != "جميع الحالات":
                if status_filter not in str(values[8]):
                    show_item = False

            # فلتر الأولوية
            if priority_filter != "جميع الأولويات":
                if priority_filter != str(values[9]):
                    show_item = False

            if show_item:
                self.tasks_tree.reattach(item, '', tk.END)
            else:
                self.tasks_tree.detach(item)

    def sort_by_column(self, column):
        """ترتيب الجدول حسب العمود"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        items = [(self.tasks_tree.set(item, column), item) for item in self.tasks_tree.get_children('')]

        # ترتيب البيانات
        items.sort()

        # إعادة ترتيب العناصر
        for index, (value, item) in enumerate(items):
            self.tasks_tree.move(item, '', index)

    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="👁️ عرض التفاصيل", command=self.view_task_details)
        self.context_menu.add_command(label="✏️ تعديل", command=self.edit_task)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="✅ تحديد كمكتمل", command=self.mark_task_complete)
        self.context_menu.add_command(label="⏸️ إيقاف مؤقت", command=self.pause_task)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📁 إدارة الملفات", command=self.manage_task_files)
        self.context_menu.add_command(label="💬 التعليقات", command=self.manage_task_comments)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ حذف", command=self.delete_task)

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        # تحديد العنصر المحدد
        item = self.tasks_tree.identify_row(event.y)
        if item:
            self.tasks_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def add_task(self):
        """إضافة مهمة جديدة"""
        messagebox.showinfo("إضافة مهمة", "نموذج إضافة مهمة جديدة\n(قيد التطوير)")

    def edit_task(self, event=None):
        """تعديل مهمة محددة"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        selected = self.tasks_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للتعديل")
            return

        task_id = self.tasks_tree.item(selected[0])['values'][0]
        messagebox.showinfo("تعديل مهمة", f"تعديل المهمة رقم {task_id}\n(قيد التطوير)")

    def edit_task_by_id(self, task_id):
        """تعديل مهمة بالمعرف"""
        messagebox.showinfo("تعديل مهمة", f"تعديل المهمة رقم {task_id}\n(قيد التطوير)")

    def view_task_details(self):
        """عرض تفاصيل المهمة"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        selected = self.tasks_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة لعرض تفاصيلها")
            return

        task_id = self.tasks_tree.item(selected[0])['values'][0]
        messagebox.showinfo("تفاصيل المهمة", f"عرض تفاصيل المهمة رقم {task_id}\n(قيد التطوير)")

    def mark_task_complete(self):
        """تحديد المهمة كمكتملة"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        selected = self.tasks_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة لتحديدها كمكتملة")
            return

        task_id = self.tasks_tree.item(selected[0])['values'][0]

        try:
            self.db_manager.cursor.execute('''
                UPDATE tasks SET status = 'مكتمل', completion_percentage = 100
                WHERE id = ?
            ''', (task_id,))
            self.db_manager.conn.commit()

            messagebox.showinfo("نجح", "تم تحديد المهمة كمكتملة")
            self.load_tasks()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث المهمة:\n{str(e)}")

    def pause_task(self):
        """إيقاف المهمة مؤقتاً"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        selected = self.tasks_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة لإيقافها")
            return

        task_id = self.tasks_tree.item(selected[0])['values'][0]

        try:
            self.db_manager.cursor.execute('''
                UPDATE tasks SET status = 'متوقف'
                WHERE id = ?
            ''', (task_id,))
            self.db_manager.conn.commit()

            messagebox.showinfo("نجح", "تم إيقاف المهمة مؤقتاً")
            self.load_tasks()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحديث المهمة:\n{str(e)}")

    def manage_task_files(self):
        """إدارة ملفات المهمة"""
        messagebox.showinfo("إدارة الملفات", "إدارة ملفات المهمة\n(قيد التطوير)")

    def manage_task_comments(self):
        """إدارة تعليقات المهمة"""
        messagebox.showinfo("إدارة التعليقات", "إدارة تعليقات المهمة\n(قيد التطوير)")

    def delete_task(self):
        """حذف مهمة محددة"""
        if not hasattr(self, 'tasks_tree') or not self.tasks_tree:
            return

        selected = self.tasks_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للحذف")
            return

        task_id = self.tasks_tree.item(selected[0])['values'][0]
        task_name = self.tasks_tree.item(selected[0])['values'][1]

        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المهمة '{task_name}'؟\n\nسيتم حذف جميع الملفات والتعليقات المرتبطة.\nهذا الإجراء لا يمكن التراجع عنه."
        )

        if result:
            try:
                # حذف المهمة والبيانات المرتبطة
                self.db_manager.cursor.execute("DELETE FROM comments WHERE entity_type = 'task' AND entity_id = ?", (task_id,))
                self.db_manager.cursor.execute("DELETE FROM attachments WHERE entity_type = 'task' AND entity_id = ?", (task_id,))
                self.db_manager.cursor.execute("DELETE FROM tasks WHERE id = ?", (task_id,))

                self.db_manager.conn.commit()

                messagebox.showinfo("نجح", "تم حذف المهمة بنجاح")
                self.load_tasks()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المهمة:\n{str(e)}")

    def export_tasks(self):
        """تصدير قائمة المهام"""
        messagebox.showinfo("تصدير المهام", "تصدير قائمة المهام\n(قيد التطوير)")
