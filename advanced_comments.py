#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة التعليقات والملاحظات المتقدمة
Advanced Comments and Notes Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

class AdvancedCommentsManager:
    """مدير التعليقات والملاحظات المتقدم"""
    
    def __init__(self, parent, db_manager, current_user, colors, fonts):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        self.comments_tree = None
        self.search_var = tk.StringVar()
        self.filter_type_var = tk.StringVar(value="جميع الأنواع")
        self.filter_project_var = tk.StringVar(value="جميع المشاريع")
        
    def create_comments_interface(self, content_area):
        """إنشاء واجهة إدارة التعليقات"""
        # مسح المحتوى السابق
        for widget in content_area.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(content_area, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان والأدوات
        self.create_header(main_frame)
        
        # شريط البحث والفلاتر
        self.create_filters_bar(main_frame)
        
        # إحصائيات التعليقات
        self.create_comments_stats(main_frame)
        
        # جدول التعليقات
        self.create_comments_table(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
        
        # تحميل البيانات
        self.load_comments()
    
    def create_header(self, parent):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(parent, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(header_frame, text="💬 إدارة التعليقات والملاحظات", 
                              font=self.fonts['title'], bg=self.colors['white'], 
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)
        
        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)
        
        # زر إضافة تعليق
        add_btn = tk.Button(tools_frame, text="➕ إضافة تعليق", 
                           font=self.fonts['button'], bg=self.colors['success'], 
                           fg=self.colors['white'], relief=tk.FLAT, 
                           command=self.add_comment, cursor="hand2")
        add_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر إضافة ملاحظة
        note_btn = tk.Button(tools_frame, text="📝 إضافة ملاحظة", 
                            font=self.fonts['button'], bg=self.colors['info'], 
                            fg=self.colors['white'], relief=tk.FLAT, 
                            command=self.add_note, cursor="hand2")
        note_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تصدير
        export_btn = tk.Button(tools_frame, text="📤 تصدير", 
                              font=self.fonts['button'], bg=self.colors['secondary'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.export_comments, cursor="hand2")
        export_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تحديث
        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث", 
                               font=self.fonts['button'], bg=self.colors['warning'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.load_comments, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
    
    def create_filters_bar(self, parent):
        """إنشاء شريط الفلاتر"""
        filters_frame = tk.Frame(parent, bg=self.colors['white'], height=60)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        filters_frame.pack_propagate(False)
        
        # البحث
        search_label = tk.Label(filters_frame, text="🔍 البحث:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        search_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        search_entry = tk.Entry(filters_frame, textvariable=self.search_var, 
                               font=self.fonts['body'], width=25, 
                               bg=self.colors['light'])
        search_entry.pack(side=tk.LEFT, padx=5, pady=15, ipady=5)
        search_entry.bind('<KeyRelease>', self.search_comments)
        
        # فلتر النوع
        type_label = tk.Label(filters_frame, text="📝 النوع:", 
                             font=self.fonts['body'], bg=self.colors['white'])
        type_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        comment_types = ["جميع الأنواع", "تعليق", "ملاحظة", "تحديث", "تنبيه"]
        type_combo = ttk.Combobox(filters_frame, textvariable=self.filter_type_var, 
                                 values=comment_types, state="readonly", width=15)
        type_combo.pack(side=tk.LEFT, padx=5, pady=15)
        type_combo.bind('<<ComboboxSelected>>', self.filter_comments)
        
        # فلتر المشروع
        project_label = tk.Label(filters_frame, text="🏗️ المشروع:", 
                                font=self.fonts['body'], bg=self.colors['white'])
        project_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        projects = self.get_projects_list()
        project_combo = ttk.Combobox(filters_frame, textvariable=self.filter_project_var, 
                                    values=projects, state="readonly", width=15)
        project_combo.pack(side=tk.LEFT, padx=5, pady=15)
        project_combo.bind('<<ComboboxSelected>>', self.filter_comments)
    
    def create_comments_stats(self, parent):
        """إنشاء إحصائيات التعليقات"""
        stats_frame = tk.Frame(parent, bg=self.colors['dark'], height=50)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        stats_frame.pack_propagate(False)
        
        # الحصول على الإحصائيات
        stats = self.get_comments_stats()
        
        # إجمالي التعليقات
        total_label = tk.Label(stats_frame, text=f"💬 إجمالي التعليقات: {stats['total_comments']}", 
                              font=self.fonts['body'], bg=self.colors['dark'], 
                              fg=self.colors['white'])
        total_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # التعليقات اليوم
        today_label = tk.Label(stats_frame, text=f"🆕 تعليقات اليوم: {stats['today_comments']}", 
                              font=self.fonts['body'], bg=self.colors['dark'], 
                              fg=self.colors['light'])
        today_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # التعليقات غير المقروءة
        unread_label = tk.Label(stats_frame, text=f"📩 غير مقروءة: {stats['unread_comments']}", 
                               font=self.fonts['body'], bg=self.colors['dark'], 
                               fg=self.colors['warning'])
        unread_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # المستخدمين النشطين
        active_label = tk.Label(stats_frame, text=f"👥 مستخدمين نشطين: {stats['active_users']}", 
                               font=self.fonts['body'], bg=self.colors['dark'], 
                               fg=self.colors['success'])
        active_label.pack(side=tk.RIGHT, padx=20, pady=12)
    
    def create_comments_table(self, parent):
        """إنشاء جدول التعليقات"""
        table_frame = tk.Frame(parent, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # أعمدة الجدول
        columns = ("ID", "النوع", "المحتوى", "المشروع/المهمة", "الكاتب", "التاريخ", "الحالة", "الردود")
        
        self.comments_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين والعرض
        column_widths = {
            "ID": 50, "النوع": 80, "المحتوى": 300, "المشروع/المهمة": 150,
            "الكاتب": 120, "التاريخ": 140, "الحالة": 80, "الردود": 80
        }
        
        for col in columns:
            self.comments_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.comments_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # شريط التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.comments_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.comments_tree.xview)
        
        self.comments_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.comments_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # قائمة السياق
        self.create_context_menu()
        
        # ربط الأحداث
        self.comments_tree.bind('<Double-1>', self.view_comment)
        self.comments_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="👁️ عرض التعليق", command=self.view_comment)
        self.context_menu.add_command(label="✏️ تعديل", command=self.edit_comment)
        self.context_menu.add_command(label="💬 رد", command=self.reply_comment)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="✅ تعيين كمقروء", command=self.mark_as_read)
        self.context_menu.add_command(label="📌 تثبيت", command=self.pin_comment)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📤 مشاركة", command=self.share_comment)
        self.context_menu.add_command(label="🗑️ حذف", command=self.delete_comment)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(parent, bg=self.colors['dark'], height=30)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="جاري التحميل...", 
                                    font=self.fonts['small'], bg=self.colors['dark'], 
                                    fg=self.colors['white'])
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # معلومات إضافية
        self.info_label = tk.Label(status_frame, text="", 
                                  font=self.fonts['small'], bg=self.colors['dark'], 
                                  fg=self.colors['light'])
        self.info_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def get_projects_list(self):
        """جلب قائمة المشاريع"""
        try:
            projects = self.db_manager.cursor.execute('''
                SELECT name FROM projects ORDER BY name
            ''').fetchall()
            project_names = ["جميع المشاريع"] + [project[0] for project in projects]
            return project_names
        except:
            return ["جميع المشاريع"]
    
    def get_comments_stats(self):
        """الحصول على إحصائيات التعليقات"""
        try:
            # إجمالي التعليقات
            total_comments = self.db_manager.cursor.execute("SELECT COUNT(*) FROM comments").fetchone()[0]
            
            # التعليقات اليوم
            today = datetime.now().strftime("%Y-%m-%d")
            today_comments = self.db_manager.cursor.execute(
                "SELECT COUNT(*) FROM comments WHERE DATE(created_date) = ?", (today,)
            ).fetchone()[0]
            
            # التعليقات غير المقروءة (تقديري)
            unread_comments = max(0, total_comments // 4)
            
            # المستخدمين النشطين
            active_users = self.db_manager.cursor.execute(
                "SELECT COUNT(DISTINCT user_id) FROM comments WHERE DATE(created_date) >= DATE('now', '-7 days')"
            ).fetchone()[0]
            
            return {
                'total_comments': total_comments,
                'today_comments': today_comments,
                'unread_comments': unread_comments,
                'active_users': active_users
            }
        except Exception as e:
            return {
                'total_comments': 0,
                'today_comments': 0,
                'unread_comments': 0,
                'active_users': 0
            }
    
    def load_comments(self):
        """تحميل قائمة التعليقات"""
        try:
            # مسح البيانات الحالية
            for item in self.comments_tree.get_children():
                self.comments_tree.delete(item)
            
            # استعلام التعليقات
            query = """
                SELECT c.id, c.comment_type, c.content, 
                       CASE 
                           WHEN c.entity_type = 'project' THEN p.name
                           WHEN c.entity_type = 'task' THEN t.name
                           ELSE 'عام'
                       END as entity_name,
                       u.full_name, c.created_date, c.is_read, 
                       (SELECT COUNT(*) FROM comments replies WHERE replies.parent_id = c.id) as reply_count
                FROM comments c
                LEFT JOIN projects p ON c.entity_type = 'project' AND c.entity_id = p.id
                LEFT JOIN tasks t ON c.entity_type = 'task' AND c.entity_id = t.id
                LEFT JOIN users u ON c.user_id = u.id
                WHERE c.parent_id IS NULL
                ORDER BY c.created_date DESC
            """
            
            comments = self.db_manager.cursor.execute(query).fetchall()
            
            # إضافة البيانات للجدول
            for comment in comments:
                # تنسيق البيانات
                comment_id = comment[0]
                comment_type = self.get_comment_type_arabic(comment[1]) if comment[1] else "تعليق"
                content = comment[2][:50] + "..." if comment[2] and len(comment[2]) > 50 else comment[2] or ""
                entity_name = comment[3] if comment[3] else "عام"
                author = comment[4] if comment[4] else "غير معروف"
                created_date = comment[5][:16] if comment[5] else ""
                status = "مقروء" if comment[6] else "غير مقروء"
                reply_count = comment[7] if comment[7] else 0
                
                # إضافة الصف
                item = self.comments_tree.insert("", tk.END, values=(
                    comment_id, comment_type, content, entity_name,
                    author, created_date, status, reply_count
                ))
                
                # تلوين الصفوف حسب الحالة
                self.color_row_by_status(item, comment[6])
            
            # تحديث شريط الحالة
            count = len(comments)
            self.status_label.config(text=f"تم تحميل {count} تعليق")
            
            # إحصائيات سريعة
            unread_count = len([c for c in comments if not c[6]])
            self.info_label.config(text=f"غير مقروء: {unread_count}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل التعليقات:\n{str(e)}")
            self.status_label.config(text="خطأ في التحميل")
    
    def get_comment_type_arabic(self, comment_type):
        """تحويل نوع التعليق إلى العربية"""
        types = {
            'comment': 'تعليق',
            'note': 'ملاحظة',
            'update': 'تحديث',
            'alert': 'تنبيه',
            'feedback': 'تقييم'
        }
        return types.get(comment_type, comment_type)
    
    def color_row_by_status(self, item, is_read):
        """تلوين الصف حسب حالة القراءة"""
        if is_read:
            # مقروء - لون عادي
            pass
        else:
            # غير مقروء - يمكن إضافة تلوين هنا
            pass

    def search_comments(self, event=None):
        """البحث في التعليقات"""
        search_term = self.search_var.get().strip().lower()

        # إخفاء/إظهار الصفوف حسب البحث
        for item in self.comments_tree.get_children():
            values = self.comments_tree.item(item)['values']
            # البحث في المحتوى والكاتب
            if (search_term in str(values[2]).lower() or
                search_term in str(values[4]).lower()):
                self.comments_tree.reattach(item, '', tk.END)
            else:
                self.comments_tree.detach(item)

    def filter_comments(self, event=None):
        """فلترة التعليقات"""
        type_filter = self.filter_type_var.get()
        project_filter = self.filter_project_var.get()

        for item in self.comments_tree.get_children():
            values = self.comments_tree.item(item)['values']
            show_item = True

            # فلتر النوع
            if type_filter != "جميع الأنواع":
                if type_filter not in str(values[1]):
                    show_item = False

            # فلتر المشروع
            if project_filter != "جميع المشاريع":
                if project_filter not in str(values[3]):
                    show_item = False

            if show_item:
                self.comments_tree.reattach(item, '', tk.END)
            else:
                self.comments_tree.detach(item)

    def sort_by_column(self, column):
        """ترتيب الجدول حسب العمود"""
        items = [(self.comments_tree.set(item, column), item) for item in self.comments_tree.get_children('')]

        # ترتيب البيانات
        items.sort()

        # إعادة ترتيب العناصر
        for index, (value, item) in enumerate(items):
            self.comments_tree.move(item, '', index)

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        # تحديد العنصر المحدد
        item = self.comments_tree.identify_row(event.y)
        if item:
            self.comments_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def add_comment(self):
        """إضافة تعليق جديد"""
        self.comment_form()

    def add_note(self):
        """إضافة ملاحظة جديدة"""
        self.comment_form(comment_type="note")

    def comment_form(self, comment_id=None, comment_type="comment"):
        """نموذج إضافة/تعديل التعليق"""
        is_edit = comment_id is not None

        # إنشاء النافذة
        form_window = tk.Toplevel(self.parent)
        title = "تعديل تعليق" if is_edit else ("إضافة ملاحظة" if comment_type == "note" else "إضافة تعليق")
        form_window.title(title)
        form_window.geometry("600x500")
        form_window.resizable(False, False)
        form_window.configure(bg=self.colors['light'])
        form_window.transient(self.parent)
        form_window.grab_set()

        # توسيط النافذة
        self.center_window(form_window, 600, 500)

        # العنوان
        icon = "📝" if comment_type == "note" else "💬"
        tk.Label(form_window, text=f"{icon} {title}",
                font=self.fonts['heading'], bg=self.colors['light'],
                fg=self.colors['primary']).pack(pady=20)

        # النموذج
        form_frame = tk.Frame(form_window, bg=self.colors['white'])
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # نوع التعليق
        tk.Label(form_frame, text="نوع التعليق:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(10, 5))
        type_var = tk.StringVar(value=comment_type)
        type_combo = ttk.Combobox(form_frame, textvariable=type_var,
                                 values=["comment", "note", "update", "alert", "feedback"],
                                 state="readonly", width=47)
        type_combo.pack(anchor=tk.W, pady=(0, 10))

        # المشروع/المهمة المرتبطة
        tk.Label(form_frame, text="المشروع المرتبط:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(0, 5))
        entity_var = tk.StringVar()
        projects = self.get_projects_list()[1:]  # بدون "جميع المشاريع"
        entity_combo = ttk.Combobox(form_frame, textvariable=entity_var,
                                   values=projects, width=47)
        entity_combo.pack(anchor=tk.W, pady=(0, 10))

        # المحتوى
        tk.Label(form_frame, text="المحتوى:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(0, 5))
        content_text = tk.Text(form_frame, height=8, font=self.fonts['body'],
                              bg=self.colors['light'], width=50, wrap=tk.WORD)
        content_text.pack(anchor=tk.W, pady=(0, 10))

        # الأولوية
        tk.Label(form_frame, text="الأولوية:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(0, 5))
        priority_var = tk.StringVar(value="متوسط")
        priority_combo = ttk.Combobox(form_frame, textvariable=priority_var,
                                     values=["منخفض", "متوسط", "عالي", "عاجل"],
                                     state="readonly", width=47)
        priority_combo.pack(anchor=tk.W, pady=(0, 10))

        # العلامات
        tk.Label(form_frame, text="العلامات (مفصولة بفواصل):", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(0, 5))
        tags_var = tk.StringVar()
        tags_entry = tk.Entry(form_frame, textvariable=tags_var,
                             font=self.fonts['body'], width=50)
        tags_entry.pack(anchor=tk.W, pady=(0, 10))

        # تحميل البيانات في حالة التعديل
        if is_edit:
            try:
                comment = self.db_manager.cursor.execute('''
                    SELECT comment_type, content, entity_type, entity_id, priority, tags
                    FROM comments WHERE id = ?
                ''', (comment_id,)).fetchone()

                if comment:
                    type_var.set(comment[0] or "comment")
                    content_text.insert("1.0", comment[1] or "")
                    # يمكن إضافة تحميل المشروع والعلامات هنا

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل بيانات التعليق:\n{str(e)}")

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(form_window, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        def save_comment():
            content = content_text.get("1.0", tk.END).strip()
            if not content:
                messagebox.showerror("خطأ", "يرجى إدخال محتوى التعليق")
                return

            try:
                # الحصول على معرف المشروع
                entity_id = None
                entity_type = "general"
                if entity_var.get():
                    project = self.db_manager.cursor.execute(
                        "SELECT id FROM projects WHERE name = ?", (entity_var.get(),)
                    ).fetchone()
                    if project:
                        entity_id = project[0]
                        entity_type = "project"

                if is_edit:
                    # تحديث التعليق
                    self.db_manager.cursor.execute('''
                        UPDATE comments SET
                            comment_type = ?, content = ?, entity_type = ?, entity_id = ?,
                            priority = ?, tags = ?
                        WHERE id = ?
                    ''', (type_var.get(), content, entity_type, entity_id,
                          priority_var.get(), tags_var.get().strip(), comment_id))
                    message = "تم تحديث التعليق بنجاح"
                else:
                    # إضافة تعليق جديد
                    self.db_manager.cursor.execute('''
                        INSERT INTO comments (
                            comment_type, content, entity_type, entity_id, priority, tags, user_id
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (type_var.get(), content, entity_type, entity_id,
                          priority_var.get(), tags_var.get().strip(), self.current_user['id']))
                    message = "تم إضافة التعليق بنجاح"

                self.db_manager.conn.commit()
                messagebox.showinfo("نجح", message)
                form_window.destroy()
                self.load_comments()

                # تسجيل النشاط
                self.log_activity("إضافة تعليق" if not is_edit else "تعديل تعليق",
                                 f"تم {'تعديل' if is_edit else 'إضافة'} تعليق")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ التعليق:\n{str(e)}")

        tk.Button(buttons_frame, text="💾 حفظ", font=self.fonts['button'],
                 bg=self.colors['success'], fg=self.colors['white'], relief=tk.FLAT,
                 command=save_comment, cursor="hand2").pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="❌ إلغاء", font=self.fonts['button'],
                 bg=self.colors['danger'], fg=self.colors['white'], relief=tk.FLAT,
                 command=form_window.destroy, cursor="hand2").pack(side=tk.RIGHT, padx=5)

    def view_comment(self, event=None):
        """عرض التعليق"""
        selected = self.comments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تعليق لعرضه")
            return

        comment_id = self.comments_tree.item(selected[0])['values'][0]
        self.show_comment_details(comment_id)

    def show_comment_details(self, comment_id):
        """عرض تفاصيل التعليق"""
        try:
            # جلب تفاصيل التعليق
            comment = self.db_manager.cursor.execute('''
                SELECT c.comment_type, c.content, c.priority, c.tags, c.created_date,
                       u.full_name, c.is_read,
                       CASE
                           WHEN c.entity_type = 'project' THEN p.name
                           WHEN c.entity_type = 'task' THEN t.name
                           ELSE 'عام'
                       END as entity_name
                FROM comments c
                LEFT JOIN users u ON c.user_id = u.id
                LEFT JOIN projects p ON c.entity_type = 'project' AND c.entity_id = p.id
                LEFT JOIN tasks t ON c.entity_type = 'task' AND c.entity_id = t.id
                WHERE c.id = ?
            ''', (comment_id,)).fetchone()

            if not comment:
                messagebox.showerror("خطأ", "لم يتم العثور على التعليق")
                return

            # إنشاء نافذة التفاصيل
            details_window = tk.Toplevel(self.parent)
            details_window.title("تفاصيل التعليق")
            details_window.geometry("700x600")
            details_window.configure(bg=self.colors['light'])
            details_window.transient(self.parent)
            details_window.grab_set()

            # توسيط النافذة
            self.center_window(details_window, 700, 600)

            # العنوان
            tk.Label(details_window, text="👁️ تفاصيل التعليق",
                    font=self.fonts['heading'], bg=self.colors['light'],
                    fg=self.colors['primary']).pack(pady=20)

            # إطار التفاصيل
            details_frame = tk.Frame(details_window, bg=self.colors['white'])
            details_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            # معلومات التعليق
            info_text = f"""
📝 النوع: {self.get_comment_type_arabic(comment[0])}
👤 الكاتب: {comment[5] or 'غير معروف'}
🏗️ المشروع/المهمة: {comment[7]}
⚡ الأولوية: {comment[2] or 'متوسط'}
🏷️ العلامات: {comment[3] or 'لا توجد'}
📅 التاريخ: {comment[4][:16] if comment[4] else ''}
📖 الحالة: {'مقروء' if comment[6] else 'غير مقروء'}
"""

            tk.Label(details_frame, text=info_text, font=self.fonts['body'],
                    bg=self.colors['white'], fg=self.colors['dark'],
                    justify=tk.LEFT).pack(anchor=tk.W, padx=20, pady=10)

            # المحتوى
            tk.Label(details_frame, text="المحتوى:", font=self.fonts['subheading'],
                    bg=self.colors['white'], fg=self.colors['primary']).pack(anchor=tk.W, padx=20, pady=(20, 5))

            content_text = tk.Text(details_frame, height=10, font=self.fonts['body'],
                                  bg=self.colors['light'], wrap=tk.WORD, state=tk.DISABLED)
            content_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

            # إدراج المحتوى
            content_text.config(state=tk.NORMAL)
            content_text.insert("1.0", comment[1] or "")
            content_text.config(state=tk.DISABLED)

            # أزرار الإجراءات
            actions_frame = tk.Frame(details_window, bg=self.colors['light'])
            actions_frame.pack(fill=tk.X, padx=20, pady=20)

            tk.Button(actions_frame, text="✏️ تعديل", font=self.fonts['button'],
                     bg=self.colors['warning'], fg=self.colors['white'], relief=tk.FLAT,
                     command=lambda: [details_window.destroy(), self.edit_comment_by_id(comment_id)],
                     cursor="hand2").pack(side=tk.LEFT, padx=5)

            tk.Button(actions_frame, text="💬 رد", font=self.fonts['button'],
                     bg=self.colors['info'], fg=self.colors['white'], relief=tk.FLAT,
                     command=lambda: [details_window.destroy(), self.reply_comment_by_id(comment_id)],
                     cursor="hand2").pack(side=tk.LEFT, padx=5)

            tk.Button(actions_frame, text="✅ تعيين كمقروء", font=self.fonts['button'],
                     bg=self.colors['success'], fg=self.colors['white'], relief=tk.FLAT,
                     command=lambda: [self.mark_as_read_by_id(comment_id), details_window.destroy()],
                     cursor="hand2").pack(side=tk.LEFT, padx=5)

            tk.Button(actions_frame, text="❌ إغلاق", font=self.fonts['button'],
                     bg=self.colors['secondary'], fg=self.colors['white'], relief=tk.FLAT,
                     command=details_window.destroy, cursor="hand2").pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل التعليق:\n{str(e)}")

    def edit_comment(self):
        """تعديل تعليق محدد"""
        selected = self.comments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تعليق للتعديل")
            return

        comment_id = self.comments_tree.item(selected[0])['values'][0]
        self.edit_comment_by_id(comment_id)

    def edit_comment_by_id(self, comment_id):
        """تعديل تعليق بالمعرف"""
        self.comment_form(comment_id)

    def reply_comment(self):
        """الرد على تعليق"""
        selected = self.comments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تعليق للرد عليه")
            return

        comment_id = self.comments_tree.item(selected[0])['values'][0]
        self.reply_comment_by_id(comment_id)

    def reply_comment_by_id(self, comment_id):
        """الرد على تعليق بالمعرف"""
        messagebox.showinfo("رد على التعليق", f"الرد على التعليق رقم {comment_id}\n(قيد التطوير)")

    def mark_as_read(self):
        """تعيين التعليق كمقروء"""
        selected = self.comments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تعليق لتعيينه كمقروء")
            return

        comment_id = self.comments_tree.item(selected[0])['values'][0]
        self.mark_as_read_by_id(comment_id)

    def mark_as_read_by_id(self, comment_id):
        """تعيين التعليق كمقروء بالمعرف"""
        try:
            self.db_manager.cursor.execute('''
                UPDATE comments SET is_read = 1 WHERE id = ?
            ''', (comment_id,))
            self.db_manager.conn.commit()

            messagebox.showinfo("نجح", "تم تعيين التعليق كمقروء")
            self.load_comments()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تعيين التعليق كمقروء:\n{str(e)}")

    def pin_comment(self):
        """تثبيت التعليق"""
        selected = self.comments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تعليق لتثبيته")
            return

        comment_id = self.comments_tree.item(selected[0])['values'][0]
        messagebox.showinfo("تثبيت التعليق", f"تثبيت التعليق رقم {comment_id}\n(قيد التطوير)")

    def share_comment(self):
        """مشاركة التعليق"""
        selected = self.comments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تعليق لمشاركته")
            return

        comment_id = self.comments_tree.item(selected[0])['values'][0]
        messagebox.showinfo("مشاركة التعليق", f"مشاركة التعليق رقم {comment_id}\n(قيد التطوير)")

    def delete_comment(self):
        """حذف التعليق"""
        selected = self.comments_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار تعليق للحذف")
            return

        comment_id = self.comments_tree.item(selected[0])['values'][0]

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                    "هل أنت متأكد من حذف هذا التعليق؟\n\nسيتم حذف جميع الردود المرتبطة أيضاً.\nهذا الإجراء لا يمكن التراجع عنه.")

        if result:
            try:
                # حذف الردود أولاً
                self.db_manager.cursor.execute("DELETE FROM comments WHERE parent_id = ?", (comment_id,))

                # حذف التعليق الأصلي
                self.db_manager.cursor.execute("DELETE FROM comments WHERE id = ?", (comment_id,))

                self.db_manager.conn.commit()

                messagebox.showinfo("نجح", "تم حذف التعليق بنجاح")
                self.load_comments()

                # تسجيل النشاط
                self.log_activity("حذف تعليق", f"تم حذف التعليق رقم {comment_id}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف التعليق:\n{str(e)}")

    def export_comments(self):
        """تصدير التعليقات"""
        messagebox.showinfo("تصدير التعليقات", "تصدير التعليقات إلى ملف\n(قيد التطوير)")

    def center_window(self, window, width, height):
        """توسيط النافذة"""
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"{width}x{height}+{x}+{y}")

    def log_activity(self, action, description):
        """تسجيل النشاط"""
        try:
            self.db_manager.cursor.execute('''
                INSERT INTO activity_log (user_id, action, entity_type, description)
                VALUES (?, ?, ?, ?)
            ''', (self.current_user['id'], action, "comments", description))
            self.db_manager.conn.commit()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")
