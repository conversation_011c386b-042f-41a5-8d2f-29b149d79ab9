#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة المشاريع المتقدمة
Advanced Projects Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
import os
from pathlib import Path

class AdvancedProjectsManager:
    """مدير المشاريع المتقدم"""
    
    def __init__(self, parent, db_manager, current_user, colors, fonts):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        self.projects_tree = None
        self.search_var = tk.StringVar()
        self.filter_var = tk.StringVar(value="جميع المشاريع")
        
    def create_projects_interface(self, content_area):
        """إنشاء واجهة إدارة المشاريع"""
        # مسح المحتوى السابق
        for widget in content_area.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(content_area, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان والأدوات
        self.create_header(main_frame)
        
        # شريط البحث والفلاتر
        self.create_search_bar(main_frame)
        
        # جدول المشاريع
        self.create_projects_table(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
        
        # تحميل البيانات
        self.load_projects()
    
    def create_header(self, parent):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(parent, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(header_frame, text="🏗️ إدارة المشاريع الهندسية", 
                              font=self.fonts['title'], bg=self.colors['white'], 
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)
        
        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)
        
        # زر مشروع جديد
        new_btn = tk.Button(tools_frame, text="➕ مشروع جديد", 
                           font=self.fonts['button'], bg=self.colors['success'], 
                           fg=self.colors['white'], relief=tk.FLAT, 
                           command=self.add_project, cursor="hand2")
        new_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تصدير
        export_btn = tk.Button(tools_frame, text="📤 تصدير", 
                              font=self.fonts['button'], bg=self.colors['info'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.export_projects, cursor="hand2")
        export_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تحديث
        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث", 
                               font=self.fonts['button'], bg=self.colors['secondary'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.load_projects, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
    
    def create_search_bar(self, parent):
        """إنشاء شريط البحث والفلاتر"""
        search_frame = tk.Frame(parent, bg=self.colors['white'], height=60)
        search_frame.pack(fill=tk.X, pady=(0, 20))
        search_frame.pack_propagate(False)
        
        # البحث
        search_label = tk.Label(search_frame, text="🔍 البحث:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        search_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, 
                               font=self.fonts['body'], width=25, 
                               bg=self.colors['light'])
        search_entry.pack(side=tk.LEFT, padx=5, pady=15, ipady=5)
        search_entry.bind('<KeyRelease>', self.search_projects)
        
        # فلتر الحالة
        filter_label = tk.Label(search_frame, text="📊 الحالة:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        filter_label.pack(side=tk.LEFT, padx=(30, 5), pady=15)
        
        filter_combo = ttk.Combobox(search_frame, textvariable=self.filter_var, 
                                   values=["جميع المشاريع", "مخطط", "قيد التنفيذ", "مكتمل", "متوقف", "ملغي"], 
                                   state="readonly", width=15)
        filter_combo.pack(side=tk.LEFT, padx=5, pady=15)
        filter_combo.bind('<<ComboboxSelected>>', self.filter_projects)
        
        # فلتر الأولوية
        priority_label = tk.Label(search_frame, text="⚡ الأولوية:", 
                                 font=self.fonts['body'], bg=self.colors['white'])
        priority_label.pack(side=tk.LEFT, padx=(30, 5), pady=15)
        
        self.priority_var = tk.StringVar(value="جميع الأولويات")
        priority_combo = ttk.Combobox(search_frame, textvariable=self.priority_var, 
                                     values=["جميع الأولويات", "منخفض", "متوسط", "عالي", "حرج"], 
                                     state="readonly", width=15)
        priority_combo.pack(side=tk.LEFT, padx=5, pady=15)
        priority_combo.bind('<<ComboboxSelected>>', self.filter_projects)
    
    def create_projects_table(self, parent):
        """إنشاء جدول المشاريع"""
        table_frame = tk.Frame(parent, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # أعمدة الجدول
        columns = ("ID", "اسم المشروع", "النوع", "الموقع", "تاريخ البداية", "تاريخ النهاية", 
                  "الميزانية المخططة", "الميزانية الفعلية", "مدير المشروع", "الحالة", "الأولوية", "نسبة الإنجاز")
        
        self.projects_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين والعرض
        column_widths = {
            "ID": 50, "اسم المشروع": 200, "النوع": 100, "الموقع": 120,
            "تاريخ البداية": 100, "تاريخ النهاية": 100, "الميزانية المخططة": 120,
            "الميزانية الفعلية": 120, "مدير المشروع": 150, "الحالة": 100, 
            "الأولوية": 80, "نسبة الإنجاز": 100
        }
        
        for col in columns:
            self.projects_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.projects_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # شريط التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.projects_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.projects_tree.xview)
        
        self.projects_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.projects_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # قائمة السياق (النقر بالزر الأيمن)
        self.create_context_menu()
        
        # ربط الأحداث
        self.projects_tree.bind('<Double-1>', self.edit_project)
        self.projects_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="👁️ عرض التفاصيل", command=self.view_project_details)
        self.context_menu.add_command(label="✏️ تعديل", command=self.edit_project)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📋 إدارة المهام", command=self.manage_tasks)
        self.context_menu.add_command(label="📁 إدارة الملفات", command=self.manage_files)
        self.context_menu.add_command(label="💬 التعليقات", command=self.manage_comments)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📊 تقرير المشروع", command=self.generate_project_report)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ حذف", command=self.delete_project)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(parent, bg=self.colors['dark'], height=30)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="جاري التحميل...", 
                                    font=self.fonts['small'], bg=self.colors['dark'], 
                                    fg=self.colors['white'])
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # معلومات إضافية
        self.info_label = tk.Label(status_frame, text="", 
                                  font=self.fonts['small'], bg=self.colors['dark'], 
                                  fg=self.colors['light'])
        self.info_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def load_projects(self):
        """تحميل قائمة المشاريع"""
        try:
            # مسح البيانات الحالية
            for item in self.projects_tree.get_children():
                self.projects_tree.delete(item)
            
            # استعلام المشاريع
            query = """
                SELECT p.id, p.name, p.project_type, p.location, p.start_date, p.end_date,
                       p.planned_budget, p.actual_budget, u.full_name, p.status, p.priority, 
                       p.completion_percentage
                FROM projects p
                LEFT JOIN users u ON p.project_manager = u.id
                ORDER BY p.created_date DESC
            """
            
            projects = self.db_manager.cursor.execute(query).fetchall()
            
            # إضافة البيانات للجدول
            for project in projects:
                # تنسيق البيانات
                project_id = project[0]
                name = project[1] if project[1] else ""
                project_type = project[2] if project[2] else ""
                location = project[3] if project[3] else ""
                start_date = project[4] if project[4] else ""
                end_date = project[5] if project[5] else ""
                planned_budget = f"{project[6]:,.0f}" if project[6] else "0"
                actual_budget = f"{project[7]:,.0f}" if project[7] else "0"
                manager = project[8] if project[8] else "غير محدد"
                status = project[9] if project[9] else "مخطط"
                priority = project[10] if project[10] else "متوسط"
                completion = f"{project[11]:.1f}%" if project[11] else "0%"
                
                # إضافة الصف
                item = self.projects_tree.insert("", tk.END, values=(
                    project_id, name, project_type, location, start_date, end_date,
                    planned_budget, actual_budget, manager, status, priority, completion
                ))
                
                # تلوين الصفوف حسب الحالة
                self.color_row_by_status(item, status)
            
            # تحديث شريط الحالة
            count = len(projects)
            self.status_label.config(text=f"تم تحميل {count} مشروع")
            
            # إحصائيات سريعة
            active_count = len([p for p in projects if p[9] == "قيد التنفيذ"])
            completed_count = len([p for p in projects if p[9] == "مكتمل"])
            self.info_label.config(text=f"نشط: {active_count} | مكتمل: {completed_count}")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشاريع:\n{str(e)}")
            self.status_label.config(text="خطأ في التحميل")
    
    def color_row_by_status(self, item, status):
        """تلوين الصف حسب الحالة"""
        colors = {
            'مخطط': '#E3F2FD',      # أزرق فاتح
            'قيد التنفيذ': '#FFF3E0', # برتقالي فاتح
            'مكتمل': '#E8F5E8',      # أخضر فاتح
            'متوقف': '#FFEBEE',      # أحمر فاتح
            'ملغي': '#F5F5F5'        # رمادي فاتح
        }
        
        color = colors.get(status, '#FFFFFF')
        self.projects_tree.set(item, "الحالة", f"● {status}")
    
    def search_projects(self, event=None):
        """البحث في المشاريع"""
        search_term = self.search_var.get().strip().lower()
        
        # إخفاء/إظهار الصفوف حسب البحث
        for item in self.projects_tree.get_children():
            values = self.projects_tree.item(item)['values']
            # البحث في اسم المشروع والموقع والنوع
            if (search_term in str(values[1]).lower() or 
                search_term in str(values[2]).lower() or 
                search_term in str(values[3]).lower()):
                self.projects_tree.reattach(item, '', tk.END)
            else:
                self.projects_tree.detach(item)
    
    def filter_projects(self, event=None):
        """فلترة المشاريع"""
        status_filter = self.filter_var.get()
        priority_filter = self.priority_var.get()
        
        for item in self.projects_tree.get_children():
            values = self.projects_tree.item(item)['values']
            show_item = True
            
            # فلتر الحالة
            if status_filter != "جميع المشاريع":
                if status_filter not in str(values[9]):
                    show_item = False
            
            # فلتر الأولوية
            if priority_filter != "جميع الأولويات":
                if priority_filter != str(values[10]):
                    show_item = False
            
            if show_item:
                self.projects_tree.reattach(item, '', tk.END)
            else:
                self.projects_tree.detach(item)
    
    def sort_by_column(self, column):
        """ترتيب الجدول حسب العمود"""
        items = [(self.projects_tree.set(item, column), item) for item in self.projects_tree.get_children('')]
        
        # ترتيب البيانات
        items.sort()
        
        # إعادة ترتيب العناصر
        for index, (value, item) in enumerate(items):
            self.projects_tree.move(item, '', index)
    
    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        # تحديد العنصر المحدد
        item = self.projects_tree.identify_row(event.y)
        if item:
            self.projects_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)
    
    def add_project(self):
        """إضافة مشروع جديد"""
        from project_form_advanced import AdvancedProjectForm
        
        form = AdvancedProjectForm(self.parent, self.db_manager, self.current_user, 
                                  self.colors, self.fonts)
        form.show()
        
        # إعادة تحميل البيانات بعد الإغلاق
        self.parent.wait_window(form.window)
        self.load_projects()
    
    def edit_project(self, event=None):
        """تعديل مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        
        from project_form_advanced import AdvancedProjectForm
        
        form = AdvancedProjectForm(self.parent, self.db_manager, self.current_user, 
                                  self.colors, self.fonts, project_id)
        form.show()
        
        # إعادة تحميل البيانات بعد الإغلاق
        self.parent.wait_window(form.window)
        self.load_projects()
    
    def view_project_details(self):
        """عرض تفاصيل المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لعرض تفاصيله")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        messagebox.showinfo("تفاصيل المشروع", f"عرض تفاصيل المشروع رقم {project_id}\n(قيد التطوير)")
    
    def manage_tasks(self):
        """إدارة مهام المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لإدارة مهامه")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        messagebox.showinfo("إدارة المهام", f"إدارة مهام المشروع رقم {project_id}\n(قيد التطوير)")
    
    def manage_files(self):
        """إدارة ملفات المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لإدارة ملفاته")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        messagebox.showinfo("إدارة الملفات", f"إدارة ملفات المشروع رقم {project_id}\n(قيد التطوير)")
    
    def manage_comments(self):
        """إدارة تعليقات المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لإدارة تعليقاته")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        messagebox.showinfo("إدارة التعليقات", f"إدارة تعليقات المشروع رقم {project_id}\n(قيد التطوير)")
    
    def generate_project_report(self):
        """إنشاء تقرير المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لإنشاء تقريره")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        messagebox.showinfo("تقرير المشروع", f"إنشاء تقرير للمشروع رقم {project_id}\n(قيد التطوير)")
    
    def delete_project(self):
        """حذف مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]
        
        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المشروع '{project_name}'؟\n\nسيتم حذف جميع المهام والملفات والتعليقات المرتبطة.\nهذا الإجراء لا يمكن التراجع عنه."
        )
        
        if result:
            try:
                # حذف المشروع والبيانات المرتبطة
                self.db_manager.cursor.execute("DELETE FROM comments WHERE entity_type = 'project' AND entity_id = ?", (project_id,))
                self.db_manager.cursor.execute("DELETE FROM attachments WHERE entity_type = 'project' AND entity_id = ?", (project_id,))
                self.db_manager.cursor.execute("DELETE FROM tasks WHERE project_id = ?", (project_id,))
                self.db_manager.cursor.execute("DELETE FROM projects WHERE id = ?", (project_id,))
                
                self.db_manager.conn.commit()
                
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
                
                # تسجيل النشاط
                self.db_manager.cursor.execute('''
                    INSERT INTO activity_log (user_id, action, entity_type, entity_id, description)
                    VALUES (?, ?, ?, ?, ?)
                ''', (self.current_user['id'], "حذف مشروع", "project", project_id, f"حذف المشروع: {project_name}"))
                self.db_manager.conn.commit()
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المشروع:\n{str(e)}")
    
    def export_projects(self):
        """تصدير قائمة المشاريع"""
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="تصدير المشاريع",
                defaultextension=".csv",
                filetypes=[("CSV files", "*.csv"), ("Excel files", "*.xlsx"), ("All files", "*.*")]
            )
            
            if file_path:
                # تصدير البيانات (مبسط)
                with open(file_path, 'w', encoding='utf-8') as f:
                    # كتابة العناوين
                    f.write("ID,اسم المشروع,النوع,الموقع,تاريخ البداية,تاريخ النهاية,الميزانية المخططة,الميزانية الفعلية,مدير المشروع,الحالة,الأولوية,نسبة الإنجاز\n")
                    
                    # كتابة البيانات
                    for item in self.projects_tree.get_children():
                        values = self.projects_tree.item(item)['values']
                        f.write(",".join([str(v) for v in values]) + "\n")
                
                messagebox.showinfo("نجح", f"تم تصدير المشاريع إلى:\n{file_path}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير المشاريع:\n{str(e)}")
