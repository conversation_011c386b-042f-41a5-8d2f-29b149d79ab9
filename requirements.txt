# متطلبات نظام إدارة المشاريع الهندسية - بلدية كفرنجة
# Engineering Project Management System Requirements - Kufranja Municipality

# Database connectivity
pyodbc>=4.0.35

# GUI Framework
tkinter  # Built-in with Python
customtkinter>=5.2.0  # Modern UI components
ttkbootstrap>=1.10.1  # Bootstrap themes for tkinter

# Report generation
reportlab>=4.0.4  # PDF generation
openpyxl>=3.1.2  # Excel file handling
python-docx>=0.8.11  # Word document generation

# Data visualization
matplotlib>=3.7.1  # Charts and graphs
pillow>=10.0.0  # Image processing

# Date and time handling
python-dateutil>=2.8.2

# Configuration and settings
configparser  # Built-in with Python

# Encryption and security
bcrypt>=4.0.1  # Password hashing
cryptography>=41.0.3  # Data encryption

# Utilities
pathlib  # Built-in with Python
os  # Built-in with Python
sys  # Built-in with Python
json  # Built-in with Python
logging  # Built-in with Python
datetime  # Built-in with Python
re  # Built-in with Python

# Optional: For advanced features
# pandas>=2.0.3  # Data analysis (if needed for complex reports)
# numpy>=1.24.3  # Numerical computations (if needed for calculations)
