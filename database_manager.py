#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مدير قاعدة البيانات لنظام إدارة المشاريع الهندسية
Database Manager for Engineering Project Management System
"""

import pyodbc
import logging
import os
from pathlib import Path
from datetime import datetime
import hashlib
import configparser

class DatabaseManager:
    """فئة إدارة قاعدة البيانات"""
    
    def __init__(self, config_file="config/settings.ini"):
        """تهيئة مدير قاعدة البيانات"""
        self.config = configparser.ConfigParser()
        self.config.read(config_file, encoding='utf-8')
        
        self.db_path = self.config.get('DATABASE', 'database_path', fallback='database/kufranja_projects.accdb')
        self.driver = self.config.get('DATABASE', 'driver', fallback='Microsoft Access Driver (*.mdb, *.accdb)')
        
        self.connection = None
        self.setup_logging()
    
    def setup_logging(self):
        """إعداد نظام السجلات"""
        logging.basicConfig(
            filename='logs/database.log',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            encoding='utf-8'
        )
        self.logger = logging.getLogger(__name__)
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            if not Path(self.db_path).exists():
                self.logger.error(f"ملف قاعدة البيانات غير موجود: {self.db_path}")
                return False
            
            conn_str = f'DRIVER={{{self.driver}}};DBQ={Path(self.db_path).absolute()};'
            self.connection = pyodbc.connect(conn_str)
            self.logger.info("تم الاتصال بقاعدة البيانات بنجاح")
            return True
            
        except pyodbc.Error as e:
            self.logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return False
    
    def disconnect(self):
        """قطع الاتصال بقاعدة البيانات"""
        if self.connection:
            self.connection.close()
            self.connection = None
            self.logger.info("تم قطع الاتصال بقاعدة البيانات")
    
    def execute_query(self, query, params=None):
        """تنفيذ استعلام قراءة"""
        try:
            if not self.connection:
                if not self.connect():
                    return None
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            results = cursor.fetchall()
            cursor.close()
            return results
            
        except pyodbc.Error as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            return None
    
    def execute_non_query(self, query, params=None):
        """تنفيذ استعلام تعديل (INSERT, UPDATE, DELETE)"""
        try:
            if not self.connection:
                if not self.connect():
                    return False
            
            cursor = self.connection.cursor()
            if params:
                cursor.execute(query, params)
            else:
                cursor.execute(query)
            
            self.connection.commit()
            cursor.close()
            return True
            
        except pyodbc.Error as e:
            self.logger.error(f"خطأ في تنفيذ الاستعلام: {e}")
            if self.connection:
                self.connection.rollback()
            return False
    
    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        hashed_password = self.hash_password(password)
        
        query = """
            SELECT u.UserID, u.Username, u.UserRole, e.FullName
            FROM Users u
            LEFT JOIN Employees e ON u.EmployeeID = e.EmployeeID
            WHERE u.Username = ? AND u.Password = ? AND u.IsActive = True
        """
        
        result = self.execute_query(query, (username, hashed_password))
        
        if result and len(result) > 0:
            user_data = {
                'user_id': result[0][0],
                'username': result[0][1],
                'role': result[0][2],
                'full_name': result[0][3] if result[0][3] else username
            }
            
            # تحديث وقت آخر دخول
            self.update_last_login(user_data['user_id'])
            
            return user_data
        
        return None
    
    def update_last_login(self, user_id):
        """تحديث وقت آخر دخول للمستخدم"""
        query = "UPDATE Users SET LastLogin = ? WHERE UserID = ?"
        self.execute_non_query(query, (datetime.now(), user_id))
    
    def get_projects(self, status=None):
        """الحصول على قائمة المشاريع"""
        query = """
            SELECT p.ProjectID, p.ProjectName, p.Status, p.StartDate, p.EndDate,
                   p.PlannedBudget, p.CompletionPercentage, e.FullName as ProjectManager
            FROM Projects p
            LEFT JOIN Employees e ON p.ProjectManager = e.EmployeeID
        """
        
        params = None
        if status:
            query += " WHERE p.Status = ?"
            params = (status,)
        
        query += " ORDER BY p.CreatedDate DESC"
        
        return self.execute_query(query, params)
    
    def add_project(self, project_data):
        """إضافة مشروع جديد"""
        query = """
            INSERT INTO Projects (ProjectName, ProjectDescription, ProjectType, Location,
                                StartDate, EndDate, PlannedBudget, ProjectManager, Status,
                                Priority, CreatedBy, CreatedDate)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        params = (
            project_data['name'],
            project_data.get('description', ''),
            project_data.get('type', ''),
            project_data.get('location', ''),
            project_data.get('start_date'),
            project_data.get('end_date'),
            project_data.get('budget', 0),
            project_data.get('manager_id'),
            project_data.get('status', 'Planned'),
            project_data.get('priority', 'Medium'),
            project_data.get('created_by'),
            datetime.now()
        )
        
        return self.execute_non_query(query, params)
    
    def update_project(self, project_id, project_data):
        """تحديث بيانات مشروع"""
        query = """
            UPDATE Projects 
            SET ProjectName = ?, ProjectDescription = ?, ProjectType = ?, Location = ?,
                StartDate = ?, EndDate = ?, PlannedBudget = ?, ProjectManager = ?,
                Status = ?, Priority = ?, LastModified = ?
            WHERE ProjectID = ?
        """
        
        params = (
            project_data['name'],
            project_data.get('description', ''),
            project_data.get('type', ''),
            project_data.get('location', ''),
            project_data.get('start_date'),
            project_data.get('end_date'),
            project_data.get('budget', 0),
            project_data.get('manager_id'),
            project_data.get('status', 'Planned'),
            project_data.get('priority', 'Medium'),
            datetime.now(),
            project_id
        )
        
        return self.execute_non_query(query, params)
    
    def delete_project(self, project_id):
        """حذف مشروع"""
        # حذف المهام المرتبطة أولاً
        self.execute_non_query("DELETE FROM Tasks WHERE ProjectID = ?", (project_id,))
        
        # حذف المشروع
        query = "DELETE FROM Projects WHERE ProjectID = ?"
        return self.execute_non_query(query, (project_id,))
    
    def get_employees(self, department_id=None):
        """الحصول على قائمة الموظفين"""
        query = """
            SELECT e.EmployeeID, e.FullName, e.Position, d.DepartmentName, e.Email, e.Phone
            FROM Employees e
            LEFT JOIN Departments d ON e.DepartmentID = d.DepartmentID
            WHERE e.IsActive = True
        """
        
        params = None
        if department_id:
            query += " AND e.DepartmentID = ?"
            params = (department_id,)
        
        query += " ORDER BY e.FullName"
        
        return self.execute_query(query, params)
    
    def get_departments(self):
        """الحصول على قائمة الأقسام"""
        query = """
            SELECT DepartmentID, DepartmentName, DepartmentHead, ContactInfo
            FROM Departments
            WHERE IsActive = True
            ORDER BY DepartmentName
        """
        
        return self.execute_query(query)
    
    def get_project_statistics(self):
        """الحصول على إحصائيات المشاريع"""
        stats = {}
        
        # إجمالي المشاريع
        result = self.execute_query("SELECT COUNT(*) FROM Projects")
        stats['total_projects'] = result[0][0] if result else 0
        
        # المشاريع النشطة
        result = self.execute_query("SELECT COUNT(*) FROM Projects WHERE Status = 'InProgress'")
        stats['active_projects'] = result[0][0] if result else 0
        
        # المشاريع المكتملة
        result = self.execute_query("SELECT COUNT(*) FROM Projects WHERE Status = 'Completed'")
        stats['completed_projects'] = result[0][0] if result else 0
        
        # إجمالي الميزانية
        result = self.execute_query("SELECT SUM(PlannedBudget) FROM Projects")
        stats['total_budget'] = result[0][0] if result and result[0][0] else 0
        
        return stats
    
    def backup_database(self):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            backup_dir = Path("backup")
            backup_dir.mkdir(exist_ok=True)
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_file = backup_dir / f"kufranja_projects_backup_{timestamp}.accdb"
            
            import shutil
            shutil.copy2(self.db_path, backup_file)
            
            self.logger.info(f"تم إنشاء نسخة احتياطية: {backup_file}")
            return str(backup_file)
            
        except Exception as e:
            self.logger.error(f"خطأ في إنشاء النسخة الاحتياطية: {e}")
            return None
