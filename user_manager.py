#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المستخدمين لنظام إدارة المشاريع الهندسية
User Management System for Engineering Project Management
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from database_manager import DatabaseManager
from datetime import datetime

class UserManager:
    """فئة إدارة المستخدمين"""
    
    def __init__(self, parent_window, db_manager, current_user):
        """تهيئة مدير المستخدمين"""
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        self.users_tree = None
        
    def show_user_management(self):
        """عرض نافذة إدارة المستخدمين"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("إدارة المستخدمين")
        self.window.geometry("1000x700")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_users()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="إدارة المستخدمين",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.window)
        toolbar_frame.pack(fill="x", padx=20, pady=(0, 10))
        
        # أزرار الأدوات
        add_button = ctk.CTkButton(
            toolbar_frame,
            text="➕ إضافة مستخدم",
            command=self.add_user,
            width=120,
            height=35
        )
        add_button.pack(side="right", padx=5, pady=10)
        
        edit_button = ctk.CTkButton(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_user,
            width=100,
            height=35,
            fg_color="#f39c12",
            hover_color="#e67e22"
        )
        edit_button.pack(side="right", padx=5, pady=10)
        
        delete_button = ctk.CTkButton(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_user,
            width=100,
            height=35,
            fg_color="#e74c3c",
            hover_color="#c0392b"
        )
        delete_button.pack(side="right", padx=5, pady=10)
        
        # حقل البحث
        search_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        search_frame.pack(side="left", padx=10, pady=10)
        
        search_label = ctk.CTkLabel(search_frame, text="البحث:")
        search_label.pack(side="left", padx=(0, 5))
        
        self.search_entry = ctk.CTkEntry(
            search_frame,
            placeholder_text="ابحث عن مستخدم...",
            width=200
        )
        self.search_entry.pack(side="left", padx=5)
        self.search_entry.bind('<KeyRelease>', self.search_users)
        
        # جدول المستخدمين
        self.setup_users_table()
    
    def setup_users_table(self):
        """إعداد جدول المستخدمين"""
        table_frame = ctk.CTkFrame(self.window)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # أعمدة الجدول
        columns = ("ID", "اسم المستخدم", "الاسم الكامل", "الصلاحية", "القسم", "الحالة", "آخر دخول")
        
        self.users_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين والعرض
        column_widths = {"ID": 50, "اسم المستخدم": 120, "الاسم الكامل": 150, 
                        "الصلاحية": 100, "القسم": 120, "الحالة": 80, "آخر دخول": 120}
        
        for col in columns:
            self.users_tree.heading(col, text=col)
            self.users_tree.column(col, width=column_widths.get(col, 100), anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.users_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.users_tree.xview)
        
        self.users_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.users_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط النقر المزدوج بالتعديل
        self.users_tree.bind('<Double-1>', lambda e: self.edit_user())
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # استعلام المستخدمين
        query = """
            SELECT u.UserID, u.Username, e.FullName, u.UserRole, d.DepartmentName, 
                   u.IsActive, u.LastLogin
            FROM Users u
            LEFT JOIN Employees e ON u.EmployeeID = e.EmployeeID
            LEFT JOIN Departments d ON e.DepartmentID = d.DepartmentID
            ORDER BY u.Username
        """
        
        users = self.db_manager.execute_query(query)
        
        if users:
            for user in users:
                # تنسيق البيانات
                user_id = user[0]
                username = user[1]
                full_name = user[2] if user[2] else "غير محدد"
                role = self.get_role_name(user[3])
                department = user[4] if user[4] else "غير محدد"
                status = "نشط" if user[5] else "غير نشط"
                last_login = user[6].strftime("%Y/%m/%d %H:%M") if user[6] else "لم يسجل دخول"
                
                # إضافة الصف
                item = self.users_tree.insert("", "end", values=(
                    user_id, username, full_name, role, department, status, last_login
                ))
                
                # تلوين الصفوف حسب الحالة
                if not user[5]:  # غير نشط
                    self.users_tree.set(item, "الحالة", "غير نشط")
    
    def get_role_name(self, role):
        """الحصول على اسم الصلاحية بالعربية"""
        roles = {
            'Admin': 'مدير النظام',
            'ProjectManager': 'مدير مشروع',
            'Employee': 'موظف',
            'SystemManager': 'مدير النظام'
        }
        return roles.get(role, role)
    
    def search_users(self, event=None):
        """البحث في المستخدمين"""
        search_term = self.search_entry.get().strip()
        
        # مسح البيانات الحالية
        for item in self.users_tree.get_children():
            self.users_tree.delete(item)
        
        # استعلام البحث
        if search_term:
            query = """
                SELECT u.UserID, u.Username, e.FullName, u.UserRole, d.DepartmentName, 
                       u.IsActive, u.LastLogin
                FROM Users u
                LEFT JOIN Employees e ON u.EmployeeID = e.EmployeeID
                LEFT JOIN Departments d ON e.DepartmentID = d.DepartmentID
                WHERE u.Username LIKE ? OR e.FullName LIKE ?
                ORDER BY u.Username
            """
            users = self.db_manager.execute_query(query, (f'%{search_term}%', f'%{search_term}%'))
        else:
            # إعادة تحميل جميع المستخدمين
            self.load_users()
            return
        
        # عرض نتائج البحث
        if users:
            for user in users:
                user_id = user[0]
                username = user[1]
                full_name = user[2] if user[2] else "غير محدد"
                role = self.get_role_name(user[3])
                department = user[4] if user[4] else "غير محدد"
                status = "نشط" if user[5] else "غير نشط"
                last_login = user[6].strftime("%Y/%m/%d %H:%M") if user[6] else "لم يسجل دخول"
                
                self.users_tree.insert("", "end", values=(
                    user_id, username, full_name, role, department, status, last_login
                ))
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        self.show_user_form()
    
    def edit_user(self):
        """تعديل مستخدم محدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return
        
        user_id = self.users_tree.item(selected[0])['values'][0]
        self.show_user_form(user_id)
    
    def delete_user(self):
        """حذف مستخدم محدد"""
        selected = self.users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للحذف")
            return
        
        user_id = self.users_tree.item(selected[0])['values'][0]
        username = self.users_tree.item(selected[0])['values'][1]
        
        # التحقق من عدم حذف المستخدم الحالي
        if user_id == self.current_user['user_id']:
            messagebox.showerror("خطأ", "لا يمكن حذف المستخدم الحالي")
            return
        
        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المستخدم '{username}'؟\nهذا الإجراء لا يمكن التراجع عنه."
        )
        
        if result:
            query = "DELETE FROM Users WHERE UserID = ?"
            if self.db_manager.execute_non_query(query, (user_id,)):
                messagebox.showinfo("نجح", "تم حذف المستخدم بنجاح")
                self.load_users()
            else:
                messagebox.showerror("خطأ", "فشل في حذف المستخدم")
    
    def show_user_form(self, user_id=None):
        """عرض نموذج إضافة/تعديل مستخدم"""
        # سيتم تطوير هذا النموذج في ملف منفصل
        from user_form import UserForm
        
        form = UserForm(self.window, self.db_manager, user_id)
        form.show()
        
        # إعادة تحميل القائمة بعد إغلاق النموذج
        self.window.wait_window(form.window)
        self.load_users()
