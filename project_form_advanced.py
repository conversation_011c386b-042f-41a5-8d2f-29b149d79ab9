#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج إضافة/تعديل المشاريع المتقدم
Advanced Project Form Module
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime, date
import os
from pathlib import Path
from tkcalendar import DateEntry

class AdvancedProjectForm:
    """نموذج المشاريع المتقدم"""

    def __init__(self, parent, db_manager, current_user, colors, fonts, project_id=None):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        self.project_id = project_id
        self.is_edit_mode = project_id is not None
        self.window = None

        # متغيرات النموذج
        self.name_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.type_var = tk.StringVar()
        self.location_var = tk.StringVar()
        self.start_date_var = tk.StringVar()
        self.end_date_var = tk.StringVar()
        self.planned_budget_var = tk.StringVar()
        self.actual_budget_var = tk.StringVar()
        self.status_var = tk.StringVar(value="مخطط")
        self.priority_var = tk.StringVar(value="متوسط")
        self.completion_var = tk.StringVar(value="0")
        self.manager_var = tk.StringVar()
        self.contractor_var = tk.StringVar()
        self.tags_var = tk.StringVar()
        self.notes_var = tk.StringVar()

    def show(self):
        """عرض النموذج"""
        self.create_window()
        self.create_interface()
        if self.is_edit_mode:
            self.load_project_data()
        self.center_window()

    def create_window(self):
        """إنشاء النافذة"""
        self.window = tk.Toplevel(self.parent)
        title = "تعديل مشروع" if self.is_edit_mode else "إضافة مشروع جديد"
        self.window.title(title)
        self.window.geometry("900x700")
        self.window.resizable(True, True)
        self.window.configure(bg=self.colors['light'])
        self.window.transient(self.parent)
        self.window.grab_set()

    def create_interface(self):
        """إنشاء واجهة النموذج"""
        # الإطار الرئيسي
        main_frame = tk.Frame(self.window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        self.create_header(main_frame)

        # إنشاء التبويبات
        self.create_tabs(main_frame)

        # أزرار الحفظ والإلغاء
        self.create_buttons(main_frame)

    def create_header(self, parent):
        """إنشاء رأس النموذج"""
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        # الأيقونة والعنوان
        icon_label = tk.Label(header_frame, text="🏗️", font=('Arial', 32),
                             bg=self.colors['primary'], fg=self.colors['white'])
        icon_label.pack(side=tk.LEFT, padx=20, pady=15)

        title_text = "تعديل مشروع" if self.is_edit_mode else "إضافة مشروع جديد"
        title_label = tk.Label(header_frame, text=title_text,
                              font=self.fonts['title'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(side=tk.LEFT, pady=20)

        # معلومات إضافية
        if self.is_edit_mode:
            info_label = tk.Label(header_frame, text=f"رقم المشروع: {self.project_id}",
                                 font=self.fonts['body'], bg=self.colors['primary'],
                                 fg=self.colors['light'])
            info_label.pack(side=tk.RIGHT, padx=20, pady=20)

    def create_tabs(self, parent):
        """إنشاء التبويبات"""
        # إطار التبويبات
        tabs_frame = tk.Frame(parent, bg=self.colors['light'])
        tabs_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # إنشاء التبويبات
        self.notebook = ttk.Notebook(tabs_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)

        # تبويب المعلومات الأساسية
        self.create_basic_info_tab()

        # تبويب التفاصيل المالية
        self.create_financial_tab()

        # تبويب الإدارة والتنظيم
        self.create_management_tab()

        # تبويب الملاحظات والمرفقات
        self.create_notes_attachments_tab()

    def create_basic_info_tab(self):
        """تبويب المعلومات الأساسية"""
        basic_frame = ttk.Frame(self.notebook)
        self.notebook.add(basic_frame, text="📋 المعلومات الأساسية")

        # إطار التمرير
        canvas = tk.Canvas(basic_frame, bg=self.colors['white'])
        scrollbar = ttk.Scrollbar(basic_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=self.colors['white'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # تخطيط التمرير
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # المحتوى
        content_frame = tk.Frame(scrollable_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # اسم المشروع
        self.create_field(content_frame, "🏗️ اسم المشروع *", self.name_var, required=True)

        # نوع المشروع
        type_frame = tk.Frame(content_frame, bg=self.colors['white'])
        type_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(type_frame, text="🏷️ نوع المشروع *", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        type_combo = ttk.Combobox(type_frame, textvariable=self.type_var,
                                 values=["بنية تحتية", "كهرباء", "مياه وصرف صحي", "طرق ومواصلات",
                                        "بيئة", "مباني عامة", "تقنية معلومات", "أخرى"],
                                 font=self.fonts['body'], width=40)
        type_combo.pack(anchor=tk.W, ipady=5)

        # الموقع
        self.create_field(content_frame, "📍 الموقع", self.location_var)

        # الوصف
        desc_frame = tk.Frame(content_frame, bg=self.colors['white'])
        desc_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(desc_frame, text="📝 وصف المشروع", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        self.description_text = tk.Text(desc_frame, height=4, font=self.fonts['body'],
                                       bg=self.colors['light'], relief=tk.FLAT, bd=5)
        self.description_text.pack(fill=tk.X, ipady=5)

        # التواريخ
        dates_frame = tk.Frame(content_frame, bg=self.colors['white'])
        dates_frame.pack(fill=tk.X, pady=(0, 20))

        # تاريخ البداية
        start_frame = tk.Frame(dates_frame, bg=self.colors['white'])
        start_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        tk.Label(start_frame, text="📅 تاريخ البداية", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        try:
            self.start_date_entry = DateEntry(start_frame, textvariable=self.start_date_var,
                                             font=self.fonts['body'], width=18,
                                             background=self.colors['secondary'],
                                             foreground='white', borderwidth=2)
            self.start_date_entry.pack(anchor=tk.W, ipady=5)
        except:
            # في حالة عدم توفر tkcalendar
            self.start_date_entry = tk.Entry(start_frame, textvariable=self.start_date_var,
                                           font=self.fonts['body'], width=20,
                                           bg=self.colors['light'])
            self.start_date_entry.pack(anchor=tk.W, ipady=5)

        # تاريخ النهاية
        end_frame = tk.Frame(dates_frame, bg=self.colors['white'])
        end_frame.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        tk.Label(end_frame, text="📅 تاريخ النهاية", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        try:
            self.end_date_entry = DateEntry(end_frame, textvariable=self.end_date_var,
                                           font=self.fonts['body'], width=18,
                                           background=self.colors['secondary'],
                                           foreground='white', borderwidth=2)
            self.end_date_entry.pack(anchor=tk.W, ipady=5)
        except:
            # في حالة عدم توفر tkcalendar
            self.end_date_entry = tk.Entry(end_frame, textvariable=self.end_date_var,
                                         font=self.fonts['body'], width=20,
                                         bg=self.colors['light'])
            self.end_date_entry.pack(anchor=tk.W, ipady=5)

        # الحالة والأولوية
        status_frame = tk.Frame(content_frame, bg=self.colors['white'])
        status_frame.pack(fill=tk.X, pady=(0, 20))

        # الحالة
        status_left = tk.Frame(status_frame, bg=self.colors['white'])
        status_left.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        tk.Label(status_left, text="📊 حالة المشروع", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        status_combo = ttk.Combobox(status_left, textvariable=self.status_var,
                                   values=["مخطط", "قيد التنفيذ", "متوقف", "مكتمل", "ملغي"],
                                   state="readonly", font=self.fonts['body'], width=18)
        status_combo.pack(anchor=tk.W, ipady=5)

        # الأولوية
        priority_right = tk.Frame(status_frame, bg=self.colors['white'])
        priority_right.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        tk.Label(priority_right, text="⚡ الأولوية", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        priority_combo = ttk.Combobox(priority_right, textvariable=self.priority_var,
                                     values=["منخفض", "متوسط", "عالي", "حرج"],
                                     state="readonly", font=self.fonts['body'], width=18)
        priority_combo.pack(anchor=tk.W, ipady=5)

        # نسبة الإنجاز
        completion_frame = tk.Frame(content_frame, bg=self.colors['white'])
        completion_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(completion_frame, text="📈 نسبة الإنجاز (%)", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        completion_scale = tk.Scale(completion_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                                   variable=self.completion_var, font=self.fonts['body'],
                                   bg=self.colors['light'], fg=self.colors['dark'],
                                   highlightthickness=0, length=400)
        completion_scale.pack(anchor=tk.W, pady=(5, 0))

    def create_financial_tab(self):
        """تبويب التفاصيل المالية"""
        financial_frame = ttk.Frame(self.notebook)
        self.notebook.add(financial_frame, text="💰 التفاصيل المالية")

        # المحتوى
        content_frame = tk.Frame(financial_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # الميزانية المخططة
        self.create_field(content_frame, "💵 الميزانية المخططة (ريال)", self.planned_budget_var,
                         field_type="number")

        # الميزانية الفعلية
        self.create_field(content_frame, "💸 الميزانية الفعلية (ريال)", self.actual_budget_var,
                         field_type="number")

        # حساب النسب المالية
        financial_info_frame = tk.Frame(content_frame, bg=self.colors['light'], relief=tk.RAISED, bd=1)
        financial_info_frame.pack(fill=tk.X, pady=20)

        tk.Label(financial_info_frame, text="📊 معلومات مالية", font=self.fonts['subheading'],
                bg=self.colors['light'], fg=self.colors['primary']).pack(pady=10)

        self.financial_info_label = tk.Label(financial_info_frame, text="",
                                            font=self.fonts['body'], bg=self.colors['light'],
                                            fg=self.colors['dark'], justify=tk.LEFT)
        self.financial_info_label.pack(pady=10)

        # ربط التحديث التلقائي
        self.planned_budget_var.trace('w', self.update_financial_info)
        self.actual_budget_var.trace('w', self.update_financial_info)

    def create_management_tab(self):
        """تبويب الإدارة والتنظيم"""
        management_frame = ttk.Frame(self.notebook)
        self.notebook.add(management_frame, text="👥 الإدارة والتنظيم")

        # المحتوى
        content_frame = tk.Frame(management_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # مدير المشروع
        manager_frame = tk.Frame(content_frame, bg=self.colors['white'])
        manager_frame.pack(fill=tk.X, pady=(0, 20))

        tk.Label(manager_frame, text="👨‍💼 مدير المشروع", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        # جلب قائمة المديرين
        managers = self.get_project_managers()
        manager_combo = ttk.Combobox(manager_frame, textvariable=self.manager_var,
                                    values=managers, font=self.fonts['body'], width=40)
        manager_combo.pack(anchor=tk.W, ipady=5)

        # المقاول
        self.create_field(content_frame, "🏢 المقاول", self.contractor_var)

        # العلامات (Tags)
        self.create_field(content_frame, "🏷️ العلامات (مفصولة بفواصل)", self.tags_var)

        # فريق العمل
        team_frame = tk.Frame(content_frame, bg=self.colors['light'], relief=tk.RAISED, bd=1)
        team_frame.pack(fill=tk.X, pady=20)

        tk.Label(team_frame, text="👥 فريق العمل", font=self.fonts['subheading'],
                bg=self.colors['light'], fg=self.colors['primary']).pack(pady=10)

        # قائمة فريق العمل (مبسطة)
        self.team_listbox = tk.Listbox(team_frame, height=5, font=self.fonts['body'],
                                      bg=self.colors['white'])
        self.team_listbox.pack(fill=tk.X, padx=20, pady=(0, 10))

        # أزرار إدارة الفريق
        team_buttons_frame = tk.Frame(team_frame, bg=self.colors['light'])
        team_buttons_frame.pack(pady=(0, 10))

        tk.Button(team_buttons_frame, text="➕ إضافة عضو", font=self.fonts['small'],
                 bg=self.colors['success'], fg=self.colors['white'], relief=tk.FLAT,
                 command=self.add_team_member, cursor="hand2").pack(side=tk.LEFT, padx=5)

        tk.Button(team_buttons_frame, text="➖ إزالة عضو", font=self.fonts['small'],
                 bg=self.colors['danger'], fg=self.colors['white'], relief=tk.FLAT,
                 command=self.remove_team_member, cursor="hand2").pack(side=tk.LEFT, padx=5)

    def create_notes_attachments_tab(self):
        """تبويب الملاحظات والمرفقات"""
        notes_frame = ttk.Frame(self.notebook)
        self.notebook.add(notes_frame, text="📝 ملاحظات ومرفقات")

        # المحتوى
        content_frame = tk.Frame(notes_frame, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # الملاحظات
        notes_section = tk.Frame(content_frame, bg=self.colors['white'])
        notes_section.pack(fill=tk.X, pady=(0, 20))

        tk.Label(notes_section, text="📝 ملاحظات إضافية", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        self.notes_text = tk.Text(notes_section, height=6, font=self.fonts['body'],
                                 bg=self.colors['light'], relief=tk.FLAT, bd=5)
        self.notes_text.pack(fill=tk.X, ipady=5)

        # المرفقات
        attachments_section = tk.Frame(content_frame, bg=self.colors['light'], relief=tk.RAISED, bd=1)
        attachments_section.pack(fill=tk.BOTH, expand=True, pady=20)

        tk.Label(attachments_section, text="📁 المرفقات", font=self.fonts['subheading'],
                bg=self.colors['light'], fg=self.colors['primary']).pack(pady=10)

        # قائمة المرفقات
        self.attachments_listbox = tk.Listbox(attachments_section, height=6, font=self.fonts['body'],
                                             bg=self.colors['white'])
        self.attachments_listbox.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 10))

        # أزرار إدارة المرفقات
        attachments_buttons_frame = tk.Frame(attachments_section, bg=self.colors['light'])
        attachments_buttons_frame.pack(pady=(0, 10))

        tk.Button(attachments_buttons_frame, text="📎 إضافة ملف", font=self.fonts['small'],
                 bg=self.colors['info'], fg=self.colors['white'], relief=tk.FLAT,
                 command=self.add_attachment, cursor="hand2").pack(side=tk.LEFT, padx=5)

        tk.Button(attachments_buttons_frame, text="👁️ عرض", font=self.fonts['small'],
                 bg=self.colors['secondary'], fg=self.colors['white'], relief=tk.FLAT,
                 command=self.view_attachment, cursor="hand2").pack(side=tk.LEFT, padx=5)

        tk.Button(attachments_buttons_frame, text="🗑️ حذف", font=self.fonts['small'],
                 bg=self.colors['danger'], fg=self.colors['white'], relief=tk.FLAT,
                 command=self.delete_attachment, cursor="hand2").pack(side=tk.LEFT, padx=5)

    def create_field(self, parent, label, variable, required=False, field_type="text"):
        """إنشاء حقل إدخال"""
        field_frame = tk.Frame(parent, bg=self.colors['white'])
        field_frame.pack(fill=tk.X, pady=(0, 20))

        label_text = label + " *" if required else label
        tk.Label(field_frame, text=label_text, font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        if field_type == "number":
            entry = tk.Entry(field_frame, textvariable=variable, font=self.fonts['body'],
                           bg=self.colors['light'], relief=tk.FLAT, bd=5, width=40)
            entry.bind('<KeyPress>', self.validate_number)
        else:
            entry = tk.Entry(field_frame, textvariable=variable, font=self.fonts['body'],
                           bg=self.colors['light'], relief=tk.FLAT, bd=5, width=40)

        entry.pack(anchor=tk.W, ipady=5)

        if required:
            entry.bind('<FocusOut>', lambda e: self.validate_required_field(variable, label))

    def create_buttons(self, parent):
        """إنشاء أزرار الحفظ والإلغاء"""
        buttons_frame = tk.Frame(parent, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X, pady=(20, 0))

        # زر الحفظ
        save_text = "💾 تحديث المشروع" if self.is_edit_mode else "💾 حفظ المشروع"
        save_button = tk.Button(buttons_frame, text=save_text,
                               font=self.fonts['button'], bg=self.colors['success'],
                               fg=self.colors['white'], relief=tk.FLAT,
                               command=self.save_project, cursor="hand2")
        save_button.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

        # زر الإلغاء
        cancel_button = tk.Button(buttons_frame, text="❌ إلغاء",
                                 font=self.fonts['button'], bg=self.colors['danger'],
                                 fg=self.colors['white'], relief=tk.FLAT,
                                 command=self.window.destroy, cursor="hand2")
        cancel_button.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

        # زر المعاينة (في وضع التعديل)
        if self.is_edit_mode:
            preview_button = tk.Button(buttons_frame, text="👁️ معاينة",
                                      font=self.fonts['button'], bg=self.colors['info'],
                                      fg=self.colors['white'], relief=tk.FLAT,
                                      command=self.preview_project, cursor="hand2")
            preview_button.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

    def center_window(self):
        """توسيط النافذة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

    def get_project_managers(self):
        """جلب قائمة مديري المشاريع"""
        try:
            managers = self.db_manager.cursor.execute('''
                SELECT full_name FROM users
                WHERE role IN ('Admin', 'ProjectManager', 'SystemManager')
                AND is_active = 1
                ORDER BY full_name
            ''').fetchall()
            return [manager[0] for manager in managers]
        except:
            return ["غير محدد"]

    def validate_number(self, event):
        """التحقق من صحة الأرقام"""
        char = event.char
        if char.isdigit() or char in ['.', '\b', '\x7f']:  # أرقام، نقطة، backspace، delete
            return True
        return "break"

    def validate_required_field(self, variable, label):
        """التحقق من الحقول المطلوبة"""
        if not variable.get().strip():
            messagebox.showwarning("حقل مطلوب", f"يرجى ملء حقل {label}")

    def update_financial_info(self, *args):
        """تحديث المعلومات المالية"""
        try:
            planned = float(self.planned_budget_var.get() or 0)
            actual = float(self.actual_budget_var.get() or 0)

            if planned > 0:
                percentage = (actual / planned) * 100
                difference = actual - planned

                info_text = f"""الميزانية المخططة: {planned:,.0f} ريال
الميزانية الفعلية: {actual:,.0f} ريال
النسبة المنفقة: {percentage:.1f}%
الفرق: {difference:,.0f} ريال"""

                if difference > 0:
                    info_text += "\n⚠️ تجاوز في الميزانية"
                elif difference < 0:
                    info_text += "\n✅ توفير في الميزانية"
                else:
                    info_text += "\n✅ ضمن الميزانية"
            else:
                info_text = "يرجى إدخال الميزانية المخططة"

            self.financial_info_label.config(text=info_text)
        except:
            self.financial_info_label.config(text="خطأ في حساب المعلومات المالية")

    def add_team_member(self):
        """إضافة عضو فريق"""
        # نافذة بسيطة لإضافة عضو
        member_window = tk.Toplevel(self.window)
        member_window.title("إضافة عضو فريق")
        member_window.geometry("400x200")
        member_window.transient(self.window)
        member_window.grab_set()

        tk.Label(member_window, text="اسم العضو:", font=self.fonts['body']).pack(pady=10)

        member_var = tk.StringVar()
        member_entry = tk.Entry(member_window, textvariable=member_var, font=self.fonts['body'], width=30)
        member_entry.pack(pady=5)
        member_entry.focus()

        def add_member():
            name = member_var.get().strip()
            if name:
                self.team_listbox.insert(tk.END, name)
                member_window.destroy()
            else:
                messagebox.showwarning("خطأ", "يرجى إدخال اسم العضو")

        tk.Button(member_window, text="إضافة", command=add_member,
                 bg=self.colors['success'], fg=self.colors['white']).pack(pady=10)

    def remove_team_member(self):
        """إزالة عضو فريق"""
        selection = self.team_listbox.curselection()
        if selection:
            self.team_listbox.delete(selection[0])
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار عضو لإزالته")

    def add_attachment(self):
        """إضافة مرفق"""
        from tkinter import filedialog

        file_path = filedialog.askopenfilename(
            title="اختيار ملف",
            filetypes=[
                ("جميع الملفات", "*.*"),
                ("ملفات PDF", "*.pdf"),
                ("ملفات Word", "*.doc;*.docx"),
                ("ملفات Excel", "*.xls;*.xlsx"),
                ("الصور", "*.jpg;*.jpeg;*.png;*.gif"),
                ("ملفات نصية", "*.txt")
            ]
        )

        if file_path:
            file_name = os.path.basename(file_path)
            self.attachments_listbox.insert(tk.END, file_name)
            # هنا يمكن نسخ الملف إلى مجلد المرفقات

    def view_attachment(self):
        """عرض مرفق"""
        selection = self.attachments_listbox.curselection()
        if selection:
            file_name = self.attachments_listbox.get(selection[0])
            messagebox.showinfo("عرض المرفق", f"عرض الملف: {file_name}\n(قيد التطوير)")
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف لعرضه")

    def delete_attachment(self):
        """حذف مرفق"""
        selection = self.attachments_listbox.curselection()
        if selection:
            result = messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المرفق؟")
            if result:
                self.attachments_listbox.delete(selection[0])
        else:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف لحذفه")

    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        try:
            project = self.db_manager.cursor.execute('''
                SELECT p.*, u.full_name as manager_name
                FROM projects p
                LEFT JOIN users u ON p.project_manager = u.id
                WHERE p.id = ?
            ''', (self.project_id,)).fetchone()

            if project:
                # تحميل البيانات الأساسية
                self.name_var.set(project[1] or "")
                self.description_text.insert("1.0", project[2] or "")
                self.type_var.set(project[3] or "")
                self.location_var.set(project[4] or "")
                self.start_date_var.set(project[5] or "")
                self.end_date_var.set(project[6] or "")
                self.planned_budget_var.set(str(project[7] or ""))
                self.actual_budget_var.set(str(project[8] or ""))
                self.status_var.set(project[9] or "مخطط")
                self.priority_var.set(project[10] or "متوسط")
                self.completion_var.set(str(project[11] or "0"))
                self.contractor_var.set(project[13] or "")
                self.tags_var.set(project[14] or "")
                self.notes_text.insert("1.0", project[18] or "")

                # تحديد مدير المشروع
                if project[19]:  # manager_name
                    self.manager_var.set(project[19])

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل بيانات المشروع:\n{str(e)}")

    def save_project(self):
        """حفظ المشروع"""
        # التحقق من البيانات المطلوبة
        if not self.name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            return

        if not self.type_var.get().strip():
            messagebox.showerror("خطأ", "يرجى اختيار نوع المشروع")
            return

        try:
            # جمع البيانات
            name = self.name_var.get().strip()
            description = self.description_text.get("1.0", tk.END).strip()
            project_type = self.type_var.get()
            location = self.location_var.get().strip()
            start_date = self.start_date_var.get() or None
            end_date = self.end_date_var.get() or None
            planned_budget = float(self.planned_budget_var.get() or 0)
            actual_budget = float(self.actual_budget_var.get() or 0)
            status = self.status_var.get()
            priority = self.priority_var.get()
            completion = float(self.completion_var.get() or 0)
            contractor = self.contractor_var.get().strip()
            tags = self.tags_var.get().strip()
            notes = self.notes_text.get("1.0", tk.END).strip()

            # الحصول على معرف مدير المشروع
            manager_id = None
            if self.manager_var.get():
                manager = self.db_manager.cursor.execute('''
                    SELECT id FROM users WHERE full_name = ? AND is_active = 1
                ''', (self.manager_var.get(),)).fetchone()
                if manager:
                    manager_id = manager[0]

            if self.is_edit_mode:
                # تحديث المشروع
                self.db_manager.cursor.execute('''
                    UPDATE projects SET
                        name = ?, description = ?, project_type = ?, location = ?,
                        start_date = ?, end_date = ?, planned_budget = ?, actual_budget = ?,
                        status = ?, priority = ?, completion_percentage = ?,
                        project_manager = ?, contractor = ?, tags = ?, notes = ?,
                        last_modified = ?
                    WHERE id = ?
                ''', (name, description, project_type, location, start_date, end_date,
                      planned_budget, actual_budget, status, priority, completion,
                      manager_id, contractor, tags, notes, datetime.now(), self.project_id))

                message = "تم تحديث المشروع بنجاح"
                action = "تحديث مشروع"
            else:
                # إضافة مشروع جديد
                self.db_manager.cursor.execute('''
                    INSERT INTO projects (
                        name, description, project_type, location, start_date, end_date,
                        planned_budget, actual_budget, status, priority, completion_percentage,
                        project_manager, contractor, tags, created_by, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (name, description, project_type, location, start_date, end_date,
                      planned_budget, actual_budget, status, priority, completion,
                      manager_id, contractor, tags, self.current_user['id'], notes))

                message = "تم إضافة المشروع بنجاح"
                action = "إضافة مشروع"

            self.db_manager.conn.commit()

            # تسجيل النشاط
            self.db_manager.cursor.execute('''
                INSERT INTO activity_log (user_id, action, entity_type, entity_id, description)
                VALUES (?, ?, ?, ?, ?)
            ''', (self.current_user['id'], action, "project",
                  self.project_id if self.is_edit_mode else self.db_manager.cursor.lastrowid,
                  f"{action}: {name}"))
            self.db_manager.conn.commit()

            messagebox.showinfo("نجح", message)
            self.window.destroy()

        except ValueError as e:
            messagebox.showerror("خطأ", "يرجى التحقق من صحة البيانات المالية")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ المشروع:\n{str(e)}")

    def preview_project(self):
        """معاينة المشروع"""
        preview_text = f"""
📋 معاينة المشروع

🏗️ الاسم: {self.name_var.get()}
🏷️ النوع: {self.type_var.get()}
📍 الموقع: {self.location_var.get()}
📊 الحالة: {self.status_var.get()}
⚡ الأولوية: {self.priority_var.get()}
📈 نسبة الإنجاز: {self.completion_var.get()}%
💰 الميزانية المخططة: {self.planned_budget_var.get()} ريال
💸 الميزانية الفعلية: {self.actual_budget_var.get()} ريال
👨‍💼 مدير المشروع: {self.manager_var.get()}
🏢 المقاول: {self.contractor_var.get()}
"""

        messagebox.showinfo("معاينة المشروع", preview_text)