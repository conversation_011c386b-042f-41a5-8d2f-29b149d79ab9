#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة البيانات Access لنظام إدارة المشاريع الهندسية
Create Access Database for Engineering Project Management System
"""

import os
import sys
from pathlib import Path

def create_access_database():
    """إنشاء قاعدة بيانات Access جديدة"""
    
    # إنشاء مجلد قاعدة البيانات إذا لم يكن موجوداً
    db_dir = Path("database")
    db_dir.mkdir(exist_ok=True)
    
    db_path = db_dir / "kufranja_projects.accdb"
    
    try:
        # استخدام COM لإنشاء قاعدة البيانات
        import win32com.client as win32
        
        # إنشاء كائن Access
        access = win32.Dispatch("Access.Application")
        
        # إنشاء قاعدة بيانات جديدة
        if db_path.exists():
            db_path.unlink()  # حذف قاعدة البيانات القديمة إذا كانت موجودة
        
        access.NewCurrentDatabase(str(db_path.absolute()))
        
        # إنشاء الجداول
        create_tables(access)
        
        # إدراج البيانات الأولية
        insert_initial_data(access)
        
        # إغلاق Access
        access.CloseCurrentDatabase()
        access.Quit()
        
        print(f"✓ تم إنشاء قاعدة البيانات بنجاح: {db_path}")
        print(f"✓ Database created successfully: {db_path}")
        
        return True
        
    except ImportError:
        print("تحذير: لم يتم العثور على مكتبة win32com")
        print("Warning: win32com library not found")
        print("سيتم إنشاء قاعدة البيانات باستخدام طريقة بديلة...")
        print("Creating database using alternative method...")
        return create_database_alternative()
        
    except Exception as e:
        print(f"خطأ في إنشاء قاعدة البيانات: {e}")
        print(f"Error creating database: {e}")
        return False

def create_database_alternative():
    """طريقة بديلة لإنشاء قاعدة البيانات باستخدام pyodbc"""
    try:
        import pyodbc
        
        db_path = Path("database/kufranja_projects.accdb").absolute()
        
        # إنشاء قاعدة بيانات فارغة
        conn_str = (
            r'DRIVER={Microsoft Access Driver (*.mdb, *.accdb)};'
            f'DBQ={db_path};'
        )
        
        # محاولة الاتصال وإنشاء الجداول
        try:
            conn = pyodbc.connect(conn_str)
            cursor = conn.cursor()
            
            # إنشاء الجداول باستخدام SQL
            create_tables_sql(cursor)
            
            # إدراج البيانات الأولية
            insert_initial_data_sql(cursor)
            
            conn.commit()
            conn.close()
            
            print(f"✓ تم إنشاء قاعدة البيانات بنجاح: {db_path}")
            return True
            
        except pyodbc.Error as e:
            print(f"خطأ في pyodbc: {e}")
            # إنشاء ملف قاعدة بيانات فارغ
            return create_empty_database_file()
            
    except ImportError:
        print("تحذير: لم يتم العثور على مكتبة pyodbc")
        return create_empty_database_file()

def create_empty_database_file():
    """إنشاء ملف قاعدة بيانات فارغ"""
    print("إنشاء ملف قاعدة بيانات فارغ...")
    print("Creating empty database file...")
    
    db_path = Path("database/kufranja_projects.accdb")
    
    # إنشاء ملف فارغ (سيحتاج المستخدم لإنشاء الجداول يدوياً)
    with open(db_path, 'wb') as f:
        # كتابة header أساسي لملف Access
        f.write(b'\x00\x01\x00\x00Standard Jet DB\x00')
    
    print(f"✓ تم إنشاء ملف قاعدة البيانات: {db_path}")
    print("ملاحظة: ستحتاج لإنشاء الجداول يدوياً باستخدام Microsoft Access")
    print("Note: You will need to create tables manually using Microsoft Access")
    
    return True

def create_tables(access):
    """إنشاء الجداول باستخدام COM"""
    
    tables_sql = [
        # جدول الأقسام
        """
        CREATE TABLE Departments (
            DepartmentID AUTOINCREMENT PRIMARY KEY,
            DepartmentName TEXT(100) NOT NULL,
            DepartmentHead TEXT(100),
            ContactInfo TEXT(255),
            CreatedDate DATETIME,
            IsActive YESNO
        )
        """,
        
        # جدول الموظفين
        """
        CREATE TABLE Employees (
            EmployeeID AUTOINCREMENT PRIMARY KEY,
            FullName TEXT(100) NOT NULL,
            DepartmentID LONG,
            Position TEXT(100),
            ContactInfo TEXT(255),
            Email TEXT(100),
            Phone TEXT(20),
            HireDate DATETIME,
            IsActive YESNO,
            CreatedDate DATETIME
        )
        """,
        
        # جدول المستخدمين
        """
        CREATE TABLE Users (
            UserID AUTOINCREMENT PRIMARY KEY,
            Username TEXT(50) NOT NULL,
            Password TEXT(255) NOT NULL,
            EmployeeID LONG,
            UserRole TEXT(50) NOT NULL,
            IsActive YESNO,
            LastLogin DATETIME,
            CreatedDate DATETIME
        )
        """,
        
        # جدول المشاريع
        """
        CREATE TABLE Projects (
            ProjectID AUTOINCREMENT PRIMARY KEY,
            ProjectName TEXT(200) NOT NULL,
            ProjectDescription MEMO,
            ProjectType TEXT(100),
            Location TEXT(255),
            StartDate DATETIME,
            EndDate DATETIME,
            PlannedBudget CURRENCY,
            ActualBudget CURRENCY,
            ProjectManager LONG,
            ContractorID LONG,
            Status TEXT(50),
            Priority TEXT(20),
            CompletionPercentage SINGLE,
            CreatedBy LONG,
            CreatedDate DATETIME,
            LastModified DATETIME
        )
        """
    ]
    
    for sql in tables_sql:
        try:
            access.DoCmd.RunSQL(sql)
        except Exception as e:
            print(f"خطأ في إنشاء جدول: {e}")

def create_tables_sql(cursor):
    """إنشاء الجداول باستخدام SQL"""
    
    tables = [
        """
        CREATE TABLE Departments (
            DepartmentID COUNTER PRIMARY KEY,
            DepartmentName TEXT(100) NOT NULL,
            DepartmentHead TEXT(100),
            ContactInfo TEXT(255),
            CreatedDate DATETIME,
            IsActive YESNO
        )
        """,
        
        """
        CREATE TABLE Employees (
            EmployeeID COUNTER PRIMARY KEY,
            FullName TEXT(100) NOT NULL,
            DepartmentID LONG,
            Position TEXT(100),
            Email TEXT(100),
            Phone TEXT(20),
            IsActive YESNO
        )
        """,
        
        """
        CREATE TABLE Users (
            UserID COUNTER PRIMARY KEY,
            Username TEXT(50) NOT NULL,
            Password TEXT(255) NOT NULL,
            EmployeeID LONG,
            UserRole TEXT(50) NOT NULL,
            IsActive YESNO
        )
        """,
        
        """
        CREATE TABLE Projects (
            ProjectID COUNTER PRIMARY KEY,
            ProjectName TEXT(200) NOT NULL,
            ProjectDescription MEMO,
            StartDate DATETIME,
            EndDate DATETIME,
            PlannedBudget CURRENCY,
            Status TEXT(50)
        )
        """
    ]
    
    for table_sql in tables:
        try:
            cursor.execute(table_sql)
        except Exception as e:
            print(f"خطأ في إنشاء جدول: {e}")

def insert_initial_data(access):
    """إدراج البيانات الأولية"""
    pass

def insert_initial_data_sql(cursor):
    """إدراج البيانات الأولية باستخدام SQL"""
    
    # إدراج مستخدم افتراضي
    try:
        cursor.execute("""
            INSERT INTO Users (Username, Password, UserRole, IsActive)
            VALUES ('admin', 'admin123', 'Admin', True)
        """)
    except:
        pass

if __name__ == "__main__":
    create_access_database()
