#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة تسجيل الدخول لنظام إدارة المشاريع الهندسية
Login Window for Engineering Project Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from database_manager import DatabaseManager

class LoginWindow:
    """فئة نافذة تسجيل الدخول"""
    
    def __init__(self, on_success_callback):
        """تهيئة نافذة تسجيل الدخول"""
        self.on_success_callback = on_success_callback
        self.db_manager = DatabaseManager()
        self.window = None
        self.setup_window()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = ctk.CTk()
        self.window.title("تسجيل الدخول - نظام إدارة المشاريع الهندسية")
        self.window.geometry("500x600")
        self.window.resizable(False, False)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_ui()
        
        # ربط مفتاح Enter بتسجيل الدخول
        self.window.bind('<Return>', lambda event: self.login())
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(expand=True, fill="both", padx=20, pady=20)
        
        # شعار البلدية
        logo_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        logo_frame.pack(pady=(20, 30))
        
        # عنوان النظام
        title_label = ctk.CTkLabel(
            logo_frame,
            text="نظام إدارة المشاريع الهندسية",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="#1f538d"
        )
        title_label.pack(pady=(0, 5))
        
        subtitle_label = ctk.CTkLabel(
            logo_frame,
            text="بلدية كفرنجة الجديدة",
            font=ctk.CTkFont(size=18),
            text_color="#666666"
        )
        subtitle_label.pack()
        
        # إطار تسجيل الدخول
        login_frame = ctk.CTkFrame(main_frame)
        login_frame.pack(pady=20, padx=40, fill="x")
        
        # عنوان تسجيل الدخول
        login_title = ctk.CTkLabel(
            login_frame,
            text="تسجيل الدخول",
            font=ctk.CTkFont(size=20, weight="bold")
        )
        login_title.pack(pady=(20, 30))
        
        # حقل اسم المستخدم
        username_label = ctk.CTkLabel(
            login_frame,
            text="اسم المستخدم:",
            font=ctk.CTkFont(size=14)
        )
        username_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.username_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="أدخل اسم المستخدم",
            font=ctk.CTkFont(size=14),
            height=40
        )
        self.username_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # حقل كلمة المرور
        password_label = ctk.CTkLabel(
            login_frame,
            text="كلمة المرور:",
            font=ctk.CTkFont(size=14)
        )
        password_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(
            login_frame,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            font=ctk.CTkFont(size=14),
            height=40
        )
        self.password_entry.pack(fill="x", padx=20, pady=(0, 20))
        
        # زر تسجيل الدخول
        self.login_button = ctk.CTkButton(
            login_frame,
            text="تسجيل الدخول",
            command=self.login,
            font=ctk.CTkFont(size=16, weight="bold"),
            height=45,
            fg_color="#1f538d",
            hover_color="#164070"
        )
        self.login_button.pack(fill="x", padx=20, pady=(0, 20))
        
        # معلومات المستخدم الافتراضي
        info_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        info_frame.pack(pady=20)
        
        info_label = ctk.CTkLabel(
            info_frame,
            text="المستخدم الافتراضي:",
            font=ctk.CTkFont(size=12, weight="bold"),
            text_color="#666666"
        )
        info_label.pack()
        
        default_user_label = ctk.CTkLabel(
            info_frame,
            text="اسم المستخدم: admin | كلمة المرور: admin123",
            font=ctk.CTkFont(size=11),
            text_color="#888888"
        )
        default_user_label.pack(pady=(5, 0))
        
        # معلومات النظام
        system_info_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        system_info_frame.pack(side="bottom", pady=10)
        
        version_label = ctk.CTkLabel(
            system_info_frame,
            text="الإصدار 1.0.0 | تطوير: فريق تقنية المعلومات",
            font=ctk.CTkFont(size=10),
            text_color="#aaaaaa"
        )
        version_label.pack()
        
        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()
    
    def login(self):
        """معالج تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()
        
        # التحقق من صحة البيانات
        if not username:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return
        
        if not password:
            messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
            self.password_entry.focus()
            return
        
        # تعطيل زر تسجيل الدخول أثناء المعالجة
        self.login_button.configure(state="disabled", text="جاري التحقق...")
        self.window.update()
        
        try:
            # محاولة تسجيل الدخول
            user_data = self.db_manager.authenticate_user(username, password)
            
            if user_data:
                # نجح تسجيل الدخول
                messagebox.showinfo(
                    "نجح تسجيل الدخول",
                    f"مرحباً {user_data['full_name']}\nتم تسجيل الدخول بنجاح"
                )
                
                # إخفاء نافذة تسجيل الدخول
                self.window.withdraw()
                
                # استدعاء دالة النجاح
                self.on_success_callback(user_data)
                
                # إغلاق النافذة
                self.window.destroy()
                
            else:
                # فشل تسجيل الدخول
                messagebox.showerror(
                    "خطأ في تسجيل الدخول",
                    "اسم المستخدم أو كلمة المرور غير صحيحة"
                )
                self.password_entry.delete(0, tk.END)
                self.username_entry.focus()
        
        except Exception as e:
            messagebox.showerror(
                "خطأ في النظام",
                f"حدث خطأ أثناء تسجيل الدخول:\n{str(e)}"
            )
        
        finally:
            # إعادة تفعيل زر تسجيل الدخول
            self.login_button.configure(state="normal", text="تسجيل الدخول")
    
    def show(self):
        """عرض النافذة"""
        self.window.deiconify()  # إظهار النافذة إذا كانت مخفية
        self.window.lift()  # رفع النافذة إلى المقدمة
        self.window.focus_force()  # تركيز على النافذة
        
    def mainloop(self):
        """تشغيل حلقة الأحداث"""
        self.window.mainloop()
