# نظام إدارة المشاريع الهندسية - بلدية كفرنجة

## Engineering Project Management System - Kufranja Municipality

### نظرة عامة | Overview

برنامج متكامل لإدارة المشاريع الهندسية مصمم خصيصاً لبلدية كفرنجة الجديدة. يتميز بواجهة مستخدم عصرية وسهلة الاستخدام، ويرتبط بقاعدة بيانات محلية (Microsoft Access) لضمان الأمان وسهولة الصيانة.

A comprehensive engineering project management system designed specifically for Kufranja New Municipality. Features a modern, user-friendly interface and connects to a local database (Microsoft Access) to ensure security and ease of maintenance.

---

## الميزات الرئيسية | Key Features

### 🏗️ إدارة المشاريع | Project Management
- إضافة وتعديل وحذف المشاريع
- تتبع تقدم المشاريع ونسب الإنجاز
- إدارة الميزانيات والتكاليف
- تعيين مديري المشاريع والمقاولين

### 👥 إدارة المستخدمين | User Management
- نظام تسجيل دخول آمن
- مستويات صلاحيات متعددة (مدير النظام، مدير مشروع، موظف)
- ربط المستخدمين بالموظفين والأقسام

### 📊 لوحة التحكم | Dashboard
- إحصائيات حية للمشاريع
- رسوم بيانية تفاعلية
- مؤشرات الأداء الرئيسية

### 📄 نظام التقارير | Reporting System
- تقارير دورية (يومية، أسبوعية، شهرية)
- تصدير بصيغ متعددة (PDF, Excel, Word)
- قوالب تقارير قابلة للتخصيص
- رسوم بيانية وإحصائيات

### 🔒 الأمان | Security
- تشفير كلمات المرور
- سجل نشاطات المستخدمين
- نسخ احتياطية تلقائية

---

## متطلبات النظام | System Requirements

### الحد الأدنى | Minimum Requirements
- **نظام التشغيل:** Windows 10/11
- **المعالج:** Intel Core i3 أو ما يعادله
- **الذاكرة:** 4 GB RAM
- **التخزين:** 2 GB مساحة فارغة
- **قاعدة البيانات:** Microsoft Access 2016 أو أحدث

### المتطلبات المستحسنة | Recommended Requirements
- **نظام التشغيل:** Windows 11
- **المعالج:** Intel Core i5 أو أحدث
- **الذاكرة:** 8 GB RAM
- **التخزين:** 5 GB مساحة فارغة
- **الشاشة:** 1920x1080 أو أعلى

---

## التثبيت والإعداد | Installation & Setup

### 1. تحميل المتطلبات | Download Requirements

```bash
# تثبيت Python 3.8 أو أحدث
# Install Python 3.8 or newer

# تثبيت المكتبات المطلوبة
# Install required packages
pip install -r requirements.txt
```

### 2. إعداد البيئة | Environment Setup

```bash
# تشغيل سكريبت الإعداد
# Run setup script
python setup.py
```

### 3. إنشاء قاعدة البيانات | Create Database

```bash
# إنشاء قاعدة البيانات
# Create database
python create_database.py
```

### 4. تشغيل البرنامج | Run Application

```bash
# تشغيل البرنامج الرئيسي
# Run main application
python main.py
```

---

## بيانات الدخول الافتراضية | Default Login Credentials

- **اسم المستخدم | Username:** `admin`
- **كلمة المرور | Password:** `admin123`

> ⚠️ **تحذير:** يرجى تغيير كلمة المرور الافتراضية فور تسجيل الدخول الأول

---

## هيكل المشروع | Project Structure

```
kufranja-project-manager/
├── main.py                 # الملف الرئيسي
├── setup.py               # سكريبت الإعداد
├── requirements.txt       # المكتبات المطلوبة
├── database_manager.py    # مدير قاعدة البيانات
├── login_window.py        # نافذة تسجيل الدخول
├── main_window.py         # النافذة الرئيسية
├── user_manager.py        # إدارة المستخدمين
├── user_form.py          # نموذج المستخدم
├── project_manager.py     # إدارة المشاريع
├── project_form.py       # نموذج المشروع
├── report_manager.py      # إدارة التقارير
├── report_generators.py  # مولدات التقارير
├── create_database.py    # إنشاء قاعدة البيانات
├── database_design.sql   # تصميم قاعدة البيانات
├── config/               # ملفات الإعدادات
├── database/             # قاعدة البيانات
├── reports/              # التقارير المُنشأة
├── logs/                 # ملفات السجلات
├── backup/               # النسخ الاحتياطية
└── attachments/          # المرفقات
```

---

## الاستخدام | Usage

### تسجيل الدخول | Login
1. تشغيل البرنامج
2. إدخال اسم المستخدم وكلمة المرور
3. النقر على "تسجيل الدخول"

### إدارة المشاريع | Project Management
1. من القائمة الجانبية، اختر "إدارة المشاريع"
2. لإضافة مشروع جديد، انقر "➕ مشروع جديد"
3. املأ البيانات المطلوبة واحفظ

### إنشاء التقارير | Generate Reports
1. من القائمة الجانبية، اختر "التقارير"
2. اختر نوع التقرير والفترة الزمنية
3. حدد تنسيق التصدير (PDF/Excel/Word)
4. انقر "📊 إنشاء التقرير"

---

## استكشاف الأخطاء | Troubleshooting

### مشاكل شائعة | Common Issues

#### خطأ في الاتصال بقاعدة البيانات
```
خطأ: لا يمكن الاتصال بقاعدة البيانات
الحل: تأكد من وجود Microsoft Access على النظام
```

#### خطأ في المكتبات
```
خطأ: ModuleNotFoundError
الحل: تشغيل pip install -r requirements.txt
```

#### مشكلة في الخطوط العربية
```
المشكلة: عدم ظهور النصوص العربية بشكل صحيح
الحل: تثبيت خطوط عربية إضافية في مجلد fonts/
```

---

## التطوير والمساهمة | Development & Contributing

### إضافة ميزات جديدة | Adding New Features
1. إنشاء فرع جديد للميزة
2. تطوير الميزة مع الاختبارات
3. تحديث التوثيق
4. إرسال طلب دمج

### معايير الكود | Code Standards
- استخدام التعليقات باللغتين العربية والإنجليزية
- اتباع معايير PEP 8 لـ Python
- كتابة اختبارات للوظائف الجديدة

---

## الدعم والصيانة | Support & Maintenance

### النسخ الاحتياطية | Backups
- نسخ احتياطية تلقائية يومية
- حفظ النسخ في مجلد `backup/`
- إمكانية استعادة البيانات من النسخ الاحتياطية

### التحديثات | Updates
- فحص التحديثات دورياً
- تطبيق التحديثات الأمنية فوراً
- اختبار التحديثات قبل التطبيق في البيئة الإنتاجية

### السجلات | Logs
- سجلات النشاطات في `logs/application.log`
- سجلات قاعدة البيانات في `logs/database.log`
- مراجعة السجلات دورياً لاكتشاف المشاكل

---

## الترخيص | License

هذا البرنامج مطور خصيصاً لبلدية كفرنجة الجديدة. جميع الحقوق محفوظة.

This software is developed specifically for Kufranja New Municipality. All rights reserved.

---

## معلومات الاتصال | Contact Information

**فريق تقنية المعلومات - بلدية كفرنجة**
IT Team - Kufranja Municipality

- **البريد الإلكتروني | Email:** <EMAIL>
- **الهاتف | Phone:** +962-X-XXXXXXX
- **العنوان | Address:** بلدية كفرنجة الجديدة، الأردن

---

## سجل التغييرات | Changelog

### الإصدار 1.0.0 | Version 1.0.0
- الإصدار الأولي
- نظام إدارة المشاريع الأساسي
- نظام إدارة المستخدمين
- نظام التقارير الأساسي
- لوحة التحكم والإحصائيات

---

*آخر تحديث: 2024/12/18*
*Last Updated: December 18, 2024*
