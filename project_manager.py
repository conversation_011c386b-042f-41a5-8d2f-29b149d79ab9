#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشاريع الهندسية
Engineering Projects Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime, date
from tkcalendar import DateEntry

class ProjectManager:
    """فئة إدارة المشاريع"""
    
    def __init__(self, parent_window, db_manager, current_user):
        """تهيئة مدير المشاريع"""
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.window = None
        self.projects_tree = None
        
    def show_project_management(self):
        """عرض نافذة إدارة المشاريع"""
        self.window = ctk.CTkToplevel(self.parent_window)
        self.window.title("إدارة المشاريع الهندسية")
        self.window.geometry("1200x800")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_projects()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_label = ctk.CTkLabel(
            self.window,
            text="إدارة المشاريع الهندسية",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        title_label.pack(pady=20)
        
        # شريط الأدوات
        toolbar_frame = ctk.CTkFrame(self.window)
        toolbar_frame.pack(fill="x", padx=20, pady=(0, 10))
        
        # أزرار الأدوات
        add_button = ctk.CTkButton(
            toolbar_frame,
            text="➕ مشروع جديد",
            command=self.add_project,
            width=120,
            height=35,
            fg_color="#27ae60",
            hover_color="#229954"
        )
        add_button.pack(side="right", padx=5, pady=10)
        
        edit_button = ctk.CTkButton(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_project,
            width=100,
            height=35,
            fg_color="#f39c12",
            hover_color="#e67e22"
        )
        edit_button.pack(side="right", padx=5, pady=10)
        
        delete_button = ctk.CTkButton(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_project,
            width=100,
            height=35,
            fg_color="#e74c3c",
            hover_color="#c0392b"
        )
        delete_button.pack(side="right", padx=5, pady=10)
        
        view_button = ctk.CTkButton(
            toolbar_frame,
            text="👁️ عرض التفاصيل",
            command=self.view_project_details,
            width=120,
            height=35,
            fg_color="#3498db",
            hover_color="#2980b9"
        )
        view_button.pack(side="right", padx=5, pady=10)
        
        # فلاتر البحث
        filter_frame = ctk.CTkFrame(toolbar_frame, fg_color="transparent")
        filter_frame.pack(side="left", padx=10, pady=10)
        
        # فلتر الحالة
        status_label = ctk.CTkLabel(filter_frame, text="الحالة:")
        status_label.pack(side="left", padx=(0, 5))
        
        self.status_filter = ctk.CTkComboBox(
            filter_frame,
            values=["جميع الحالات", "مخطط", "قيد التنفيذ", "مكتمل", "متوقف", "ملغي"],
            width=120,
            command=self.filter_projects
        )
        self.status_filter.pack(side="left", padx=5)
        self.status_filter.set("جميع الحالات")
        
        # حقل البحث
        search_label = ctk.CTkLabel(filter_frame, text="البحث:")
        search_label.pack(side="left", padx=(20, 5))
        
        self.search_entry = ctk.CTkEntry(
            filter_frame,
            placeholder_text="ابحث في المشاريع...",
            width=200
        )
        self.search_entry.pack(side="left", padx=5)
        self.search_entry.bind('<KeyRelease>', self.search_projects)
        
        # جدول المشاريع
        self.setup_projects_table()
    
    def setup_projects_table(self):
        """إعداد جدول المشاريع"""
        table_frame = ctk.CTkFrame(self.window)
        table_frame.pack(fill="both", expand=True, padx=20, pady=10)
        
        # أعمدة الجدول
        columns = ("ID", "اسم المشروع", "النوع", "الموقع", "تاريخ البداية", "تاريخ النهاية", 
                  "الميزانية المخططة", "الميزانية الفعلية", "مدير المشروع", "الحالة", "نسبة الإنجاز")
        
        self.projects_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين والعرض
        column_widths = {
            "ID": 50, "اسم المشروع": 200, "النوع": 100, "الموقع": 150,
            "تاريخ البداية": 100, "تاريخ النهاية": 100, "الميزانية المخططة": 120,
            "الميزانية الفعلية": 120, "مدير المشروع": 150, "الحالة": 80, "نسبة الإنجاز": 100
        }
        
        for col in columns:
            self.projects_tree.heading(col, text=col)
            self.projects_tree.column(col, width=column_widths.get(col, 100), anchor="center")
        
        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient="vertical", command=self.projects_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient="horizontal", command=self.projects_tree.xview)
        
        self.projects_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تخطيط الجدول
        self.projects_tree.grid(row=0, column=0, sticky="nsew")
        scrollbar_y.grid(row=0, column=1, sticky="ns")
        scrollbar_x.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # ربط النقر المزدوج بعرض التفاصيل
        self.projects_tree.bind('<Double-1>', lambda e: self.view_project_details())
    
    def load_projects(self, status_filter=None, search_term=None):
        """تحميل قائمة المشاريع"""
        # مسح البيانات الحالية
        for item in self.projects_tree.get_children():
            self.projects_tree.delete(item)
        
        # بناء الاستعلام
        query = """
            SELECT p.ProjectID, p.ProjectName, p.ProjectType, p.Location, p.StartDate, p.EndDate,
                   p.PlannedBudget, p.ActualBudget, e.FullName as ProjectManager, p.Status, 
                   p.CompletionPercentage
            FROM Projects p
            LEFT JOIN Employees e ON p.ProjectManager = e.EmployeeID
        """
        
        conditions = []
        params = []
        
        # فلتر الحالة
        if status_filter and status_filter != "جميع الحالات":
            status_map = {
                "مخطط": "Planned",
                "قيد التنفيذ": "InProgress", 
                "مكتمل": "Completed",
                "متوقف": "OnHold",
                "ملغي": "Cancelled"
            }
            if status_filter in status_map:
                conditions.append("p.Status = ?")
                params.append(status_map[status_filter])
        
        # فلتر البحث
        if search_term:
            conditions.append("(p.ProjectName LIKE ? OR p.Location LIKE ? OR p.ProjectType LIKE ?)")
            params.extend([f'%{search_term}%', f'%{search_term}%', f'%{search_term}%'])
        
        # إضافة الشروط للاستعلام
        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        
        query += " ORDER BY p.CreatedDate DESC"
        
        # تنفيذ الاستعلام
        projects = self.db_manager.execute_query(query, params if params else None)
        
        if projects:
            for project in projects:
                # تنسيق البيانات
                project_id = project[0]
                name = project[1] if project[1] else ""
                project_type = project[2] if project[2] else ""
                location = project[3] if project[3] else ""
                start_date = project[4].strftime("%Y/%m/%d") if project[4] else ""
                end_date = project[5].strftime("%Y/%m/%d") if project[5] else ""
                planned_budget = f"{project[6]:,.0f}" if project[6] else "0"
                actual_budget = f"{project[7]:,.0f}" if project[7] else "0"
                manager = project[8] if project[8] else "غير محدد"
                status = self.get_status_name(project[9])
                completion = f"{project[10]:.1f}%" if project[10] else "0%"
                
                # إضافة الصف
                item = self.projects_tree.insert("", "end", values=(
                    project_id, name, project_type, location, start_date, end_date,
                    planned_budget, actual_budget, manager, status, completion
                ))
                
                # تلوين الصفوف حسب الحالة
                if project[9] == "Completed":
                    self.projects_tree.set(item, "الحالة", "مكتمل ✅")
                elif project[9] == "InProgress":
                    self.projects_tree.set(item, "الحالة", "قيد التنفيذ 🔄")
                elif project[9] == "OnHold":
                    self.projects_tree.set(item, "الحالة", "متوقف ⏸️")
                elif project[9] == "Cancelled":
                    self.projects_tree.set(item, "الحالة", "ملغي ❌")
    
    def get_status_name(self, status):
        """الحصول على اسم الحالة بالعربية"""
        status_map = {
            "Planned": "مخطط",
            "InProgress": "قيد التنفيذ",
            "Completed": "مكتمل",
            "OnHold": "متوقف",
            "Cancelled": "ملغي"
        }
        return status_map.get(status, status)
    
    def filter_projects(self, selected_status=None):
        """فلترة المشاريع حسب الحالة"""
        status = self.status_filter.get()
        search_term = self.search_entry.get().strip()
        self.load_projects(status, search_term if search_term else None)
    
    def search_projects(self, event=None):
        """البحث في المشاريع"""
        search_term = self.search_entry.get().strip()
        status = self.status_filter.get()
        self.load_projects(status if status != "جميع الحالات" else None, search_term if search_term else None)
    
    def add_project(self):
        """إضافة مشروع جديد"""
        from project_form import ProjectForm
        
        form = ProjectForm(self.window, self.db_manager, self.current_user)
        form.show()
        
        # إعادة تحميل القائمة بعد إغلاق النموذج
        self.window.wait_window(form.window)
        self.load_projects()
    
    def edit_project(self):
        """تعديل مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        
        from project_form import ProjectForm
        
        form = ProjectForm(self.window, self.db_manager, self.current_user, project_id)
        form.show()
        
        # إعادة تحميل القائمة بعد إغلاق النموذج
        self.window.wait_window(form.window)
        self.load_projects()
    
    def delete_project(self):
        """حذف مشروع محدد"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للحذف")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        project_name = self.projects_tree.item(selected[0])['values'][1]
        
        # تأكيد الحذف
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المشروع '{project_name}'؟\n\nسيتم حذف جميع المهام والبيانات المرتبطة بهذا المشروع.\nهذا الإجراء لا يمكن التراجع عنه."
        )
        
        if result:
            if self.db_manager.delete_project(project_id):
                messagebox.showinfo("نجح", "تم حذف المشروع بنجاح")
                self.load_projects()
            else:
                messagebox.showerror("خطأ", "فشل في حذف المشروع")
    
    def view_project_details(self):
        """عرض تفاصيل المشروع"""
        selected = self.projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع لعرض تفاصيله")
            return
        
        project_id = self.projects_tree.item(selected[0])['values'][0]
        
        from project_details import ProjectDetails
        
        details = ProjectDetails(self.window, self.db_manager, project_id)
        details.show()
