#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشاريع الهندسية - بلدية كفرنجة
Engineering Project Management System - Kufranja Municipality

الملف الرئيسي للبرنامج
Main Application File
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from pathlib import Path
import sys
import os

# إضافة مسار المشروع إلى sys.path
sys.path.append(str(Path(__file__).parent))

from database_manager import DatabaseManager
from login_window import LoginWindow
from main_window import MainWindow

class KufranjaProjectManager:
    """الفئة الرئيسية لنظام إدارة المشاريع"""
    
    def __init__(self):
        """تهيئة التطبيق"""
        self.setup_theme()
        self.db_manager = DatabaseManager()
        self.current_user = None
        self.main_window = None
        
        # التحقق من وجود قاعدة البيانات
        if not self.check_database():
            self.create_database_prompt()
            return
        
        # عرض نافذة تسجيل الدخول
        self.show_login()
    
    def setup_theme(self):
        """إعداد المظهر العام للتطبيق"""
        ctk.set_appearance_mode("light")  # أو "dark" أو "system"
        ctk.set_default_color_theme("blue")  # أو "green" أو "dark-blue"
    
    def check_database(self):
        """التحقق من وجود قاعدة البيانات"""
        db_path = Path("database/kufranja_projects.accdb")
        return db_path.exists()
    
    def create_database_prompt(self):
        """عرض رسالة لإنشاء قاعدة البيانات"""
        root = ctk.CTk()
        root.withdraw()  # إخفاء النافذة الرئيسية
        
        result = messagebox.askyesno(
            "قاعدة البيانات غير موجودة",
            "لم يتم العثور على قاعدة البيانات.\nهل تريد إنشاء قاعدة بيانات جديدة؟",
            icon="question"
        )
        
        if result:
            self.create_database()
        else:
            root.quit()
            sys.exit()
    
    def create_database(self):
        """إنشاء قاعدة البيانات"""
        try:
            from create_database import create_access_database
            
            if create_access_database():
                messagebox.showinfo(
                    "نجح الإنشاء",
                    "تم إنشاء قاعدة البيانات بنجاح!\nيمكنك الآن تسجيل الدخول باستخدام:\nاسم المستخدم: admin\nكلمة المرور: admin123"
                )
                self.show_login()
            else:
                messagebox.showerror(
                    "خطأ",
                    "فشل في إنشاء قاعدة البيانات.\nيرجى التحقق من وجود Microsoft Access على النظام."
                )
                sys.exit()
                
        except Exception as e:
            messagebox.showerror(
                "خطأ",
                f"حدث خطأ أثناء إنشاء قاعدة البيانات:\n{str(e)}"
            )
            sys.exit()
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        login_window = LoginWindow(self.on_login_success)
        login_window.show()
    
    def on_login_success(self, user_data):
        """معالج نجاح تسجيل الدخول"""
        self.current_user = user_data
        self.show_main_window()
    
    def show_main_window(self):
        """عرض النافذة الرئيسية"""
        if self.main_window:
            self.main_window.destroy()
        
        self.main_window = MainWindow(self.current_user, self.db_manager)
        self.main_window.show()
    
    def run(self):
        """تشغيل التطبيق"""
        # إنشاء حلقة الأحداث الرئيسية
        if hasattr(self, 'main_window') and self.main_window:
            self.main_window.mainloop()

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من وجود المجلدات المطلوبة
        required_dirs = ["database", "logs", "config", "reports", "backup"]
        for directory in required_dirs:
            Path(directory).mkdir(exist_ok=True)
        
        # تشغيل التطبيق
        app = KufranjaProjectManager()
        
        # إذا تم إنشاء النافذة الرئيسية، تشغيل حلقة الأحداث
        if hasattr(app, 'main_window') and app.main_window:
            app.main_window.mainloop()
        
    except Exception as e:
        # عرض رسالة خطأ في حالة حدوث مشكلة
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror(
            "خطأ في التطبيق",
            f"حدث خطأ أثناء تشغيل التطبيق:\n{str(e)}\n\nيرجى التحقق من:\n1. تثبيت جميع المكتبات المطلوبة\n2. وجود Microsoft Access على النظام\n3. صلاحيات الكتابة في مجلد التطبيق"
        )
        sys.exit(1)

if __name__ == "__main__":
    main()
