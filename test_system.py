#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام إدارة المشاريع الهندسية
System Test for Engineering Project Management System
"""

import sys
import os
import unittest
from pathlib import Path
from datetime import datetime, date

# إضافة مسار المشروع
sys.path.append(str(Path(__file__).parent))

class TestSystemRequirements(unittest.TestCase):
    """اختبار متطلبات النظام"""
    
    def test_python_version(self):
        """اختبار إصدار Python"""
        self.assertGreaterEqual(sys.version_info[:2], (3, 8), 
                               "يتطلب Python 3.8 أو أحدث")
    
    def test_required_modules(self):
        """اختبار المكتبات المطلوبة"""
        required_modules = [
            'tkinter',
            'pathlib',
            'datetime',
            'hashlib',
            'configparser',
            'logging',
            'os',
            'sys'
        ]
        
        for module in required_modules:
            try:
                __import__(module)
            except ImportError:
                self.fail(f"المكتبة المطلوبة غير متوفرة: {module}")
    
    def test_optional_modules(self):
        """اختبار المكتبات الاختيارية"""
        optional_modules = {
            'customtkinter': 'واجهة المستخدم الحديثة',
            'pyodbc': 'الاتصال بقاعدة البيانات',
            'reportlab': 'إنشاء تقارير PDF',
            'openpyxl': 'إنشاء تقارير Excel',
            'docx': 'إنشاء تقارير Word',
            'matplotlib': 'الرسوم البيانية',
            'tkcalendar': 'اختيار التواريخ'
        }
        
        missing_modules = []
        for module, description in optional_modules.items():
            try:
                __import__(module)
                print(f"✓ {module} - {description}")
            except ImportError:
                missing_modules.append(f"{module} - {description}")
                print(f"✗ {module} - {description}")
        
        if missing_modules:
            print(f"\nتحذير: المكتبات التالية غير مثبتة:")
            for module in missing_modules:
                print(f"  - {module}")
            print("يرجى تشغيل: pip install -r requirements.txt")

class TestDirectoryStructure(unittest.TestCase):
    """اختبار هيكل المجلدات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        self.base_path = Path(__file__).parent
        self.required_dirs = [
            'database',
            'reports', 
            'logs',
            'config',
            'backup',
            'attachments'
        ]
    
    def test_create_directories(self):
        """اختبار إنشاء المجلدات المطلوبة"""
        for directory in self.required_dirs:
            dir_path = self.base_path / directory
            dir_path.mkdir(exist_ok=True)
            self.assertTrue(dir_path.exists(), f"فشل في إنشاء مجلد: {directory}")
            print(f"✓ تم إنشاء مجلد: {directory}")

class TestDatabaseManager(unittest.TestCase):
    """اختبار مدير قاعدة البيانات"""
    
    def setUp(self):
        """إعداد الاختبار"""
        try:
            from database_manager import DatabaseManager
            self.db_manager = DatabaseManager()
        except ImportError as e:
            self.skipTest(f"لا يمكن استيراد DatabaseManager: {e}")
    
    def test_database_manager_init(self):
        """اختبار تهيئة مدير قاعدة البيانات"""
        self.assertIsNotNone(self.db_manager)
        self.assertIsNotNone(self.db_manager.db_path)
        print("✓ تم تهيئة مدير قاعدة البيانات بنجاح")
    
    def test_password_hashing(self):
        """اختبار تشفير كلمات المرور"""
        password = "test123"
        hashed = self.db_manager.hash_password(password)
        
        self.assertIsNotNone(hashed)
        self.assertNotEqual(password, hashed)
        self.assertEqual(len(hashed), 64)  # SHA256 hex length
        print("✓ تشفير كلمات المرور يعمل بشكل صحيح")

class TestConfigurationFiles(unittest.TestCase):
    """اختبار ملفات الإعدادات"""
    
    def test_settings_file(self):
        """اختبار ملف الإعدادات"""
        settings_path = Path("config/settings.ini")
        
        if not settings_path.exists():
            # إنشاء ملف الإعدادات إذا لم يكن موجوداً
            settings_path.parent.mkdir(exist_ok=True)
            
            config_content = """[DATABASE]
driver = Microsoft Access Driver (*.mdb, *.accdb)
database_path = database/kufranja_projects.accdb

[APPLICATION]
app_name = نظام إدارة المشاريع الهندسية - بلدية كفرنجة
version = 1.0.0
language = ar

[SECURITY]
session_timeout = 30
password_min_length = 8
"""
            with open(settings_path, 'w', encoding='utf-8') as f:
                f.write(config_content)
        
        self.assertTrue(settings_path.exists(), "ملف الإعدادات غير موجود")
        
        # اختبار قراءة الملف
        import configparser
        config = configparser.ConfigParser()
        config.read(settings_path, encoding='utf-8')
        
        self.assertTrue(config.has_section('DATABASE'), "قسم DATABASE غير موجود")
        self.assertTrue(config.has_section('APPLICATION'), "قسم APPLICATION غير موجود")
        print("✓ ملف الإعدادات صحيح")

class TestMainApplication(unittest.TestCase):
    """اختبار التطبيق الرئيسي"""
    
    def test_main_file_exists(self):
        """اختبار وجود الملف الرئيسي"""
        main_file = Path("main.py")
        self.assertTrue(main_file.exists(), "الملف الرئيسي main.py غير موجود")
        print("✓ الملف الرئيسي موجود")
    
    def test_import_main_modules(self):
        """اختبار استيراد الوحدات الرئيسية"""
        modules_to_test = [
            'database_manager',
            'login_window', 
            'main_window',
            'user_manager',
            'user_form',
            'project_manager',
            'project_form',
            'report_manager',
            'report_generators'
        ]
        
        successful_imports = []
        failed_imports = []
        
        for module in modules_to_test:
            try:
                __import__(module)
                successful_imports.append(module)
                print(f"✓ {module}")
            except ImportError as e:
                failed_imports.append((module, str(e)))
                print(f"✗ {module}: {e}")
        
        if failed_imports:
            print(f"\nتحذير: فشل في استيراد {len(failed_imports)} وحدة:")
            for module, error in failed_imports:
                print(f"  - {module}: {error}")
        
        print(f"\nنجح استيراد {len(successful_imports)} من {len(modules_to_test)} وحدة")

def run_system_check():
    """تشغيل فحص شامل للنظام"""
    print("=" * 60)
    print("فحص نظام إدارة المشاريع الهندسية - بلدية كفرنجة")
    print("Engineering Project Management System Check - Kufranja Municipality")
    print("=" * 60)
    print()
    
    # تشغيل الاختبارات
    test_classes = [
        TestSystemRequirements,
        TestDirectoryStructure,
        TestDatabaseManager,
        TestConfigurationFiles,
        TestMainApplication
    ]
    
    total_tests = 0
    passed_tests = 0
    failed_tests = 0
    
    for test_class in test_classes:
        print(f"\n--- {test_class.__doc__} ---")
        
        suite = unittest.TestLoader().loadTestsFromTestCase(test_class)
        runner = unittest.TextTestRunner(verbosity=0, stream=open(os.devnull, 'w'))
        result = runner.run(suite)
        
        total_tests += result.testsRun
        passed_tests += result.testsRun - len(result.failures) - len(result.errors)
        failed_tests += len(result.failures) + len(result.errors)
        
        if result.failures:
            print("فشل في الاختبارات التالية:")
            for test, error in result.failures:
                print(f"  ✗ {test}: {error}")
        
        if result.errors:
            print("أخطاء في الاختبارات التالية:")
            for test, error in result.errors:
                print(f"  ✗ {test}: {error}")
    
    print("\n" + "=" * 60)
    print("ملخص النتائج | Results Summary")
    print("=" * 60)
    print(f"إجمالي الاختبارات | Total Tests: {total_tests}")
    print(f"نجح | Passed: {passed_tests}")
    print(f"فشل | Failed: {failed_tests}")
    
    if failed_tests == 0:
        print("\n✅ جميع الاختبارات نجحت! النظام جاهز للتشغيل")
        print("✅ All tests passed! System is ready to run")
    else:
        print(f"\n⚠️  {failed_tests} اختبار فشل. يرجى مراجعة الأخطاء أعلاه")
        print(f"⚠️  {failed_tests} tests failed. Please review the errors above")
    
    print("\nلتشغيل البرنامج:")
    print("To run the application:")
    print("  Windows: run.bat")
    print("  Python: python main.py")
    print("=" * 60)

if __name__ == "__main__":
    run_system_check()
