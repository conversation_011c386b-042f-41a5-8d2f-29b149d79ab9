#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج إضافة وتعديل المشاريع
Project Add/Edit Form
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime, date
from tkcalendar import DateEntry

class ProjectForm:
    """فئة نموذج المشروع"""
    
    def __init__(self, parent_window, db_manager, current_user, project_id=None):
        """تهيئة النموذج"""
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.current_user = current_user
        self.project_id = project_id
        self.window = None
        self.is_edit_mode = project_id is not None
        
        # متغيرات النموذج
        self.project_name_var = tk.StringVar()
        self.project_type_var = tk.StringVar()
        self.location_var = tk.StringVar()
        self.description_var = tk.StringVar()
        self.planned_budget_var = tk.StringVar()
        self.actual_budget_var = tk.StringVar()
        self.manager_var = tk.StringVar()
        self.contractor_var = tk.StringVar()
        self.status_var = tk.StringVar()
        self.priority_var = tk.StringVar()
        self.completion_var = tk.StringVar()
        
        self.employees_data = []
        self.contractors_data = []
        
    def show(self):
        """عرض النموذج"""
        self.window = ctk.CTkToplevel(self.parent_window)
        title = "تعديل مشروع" if self.is_edit_mode else "إضافة مشروع جديد"
        self.window.title(title)
        self.window.geometry("800x900")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_data()
        
        if self.is_edit_mode:
            self.load_project_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title = "تعديل مشروع" if self.is_edit_mode else "إضافة مشروع جديد"
        title_label = ctk.CTkLabel(
            self.window,
            text=title,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # إطار التمرير
        scroll_frame = ctk.CTkScrollableFrame(self.window)
        scroll_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # معلومات أساسية
        basic_frame = ctk.CTkFrame(scroll_frame)
        basic_frame.pack(fill="x", pady=(0, 20))
        
        basic_title = ctk.CTkLabel(
            basic_frame,
            text="المعلومات الأساسية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        basic_title.pack(pady=(15, 10))
        
        # اسم المشروع
        name_label = ctk.CTkLabel(basic_frame, text="اسم المشروع *:")
        name_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.name_entry = ctk.CTkEntry(
            basic_frame,
            textvariable=self.project_name_var,
            placeholder_text="أدخل اسم المشروع",
            height=35
        )
        self.name_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # نوع المشروع
        type_label = ctk.CTkLabel(basic_frame, text="نوع المشروع:")
        type_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        project_types = ["بنية تحتية", "مباني", "طرق", "شبكات", "حدائق", "أخرى"]
        self.type_combo = ctk.CTkComboBox(
            basic_frame,
            variable=self.project_type_var,
            values=project_types,
            height=35
        )
        self.type_combo.pack(fill="x", padx=20, pady=(0, 15))
        
        # الموقع
        location_label = ctk.CTkLabel(basic_frame, text="الموقع:")
        location_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.location_entry = ctk.CTkEntry(
            basic_frame,
            textvariable=self.location_var,
            placeholder_text="أدخل موقع المشروع",
            height=35
        )
        self.location_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # الوصف
        desc_label = ctk.CTkLabel(basic_frame, text="وصف المشروع:")
        desc_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.desc_text = ctk.CTkTextbox(
            basic_frame,
            height=80,
            placeholder_text="أدخل وصف تفصيلي للمشروع..."
        )
        self.desc_text.pack(fill="x", padx=20, pady=(0, 20))
        
        # التواريخ والميزانية
        dates_frame = ctk.CTkFrame(scroll_frame)
        dates_frame.pack(fill="x", pady=(0, 20))
        
        dates_title = ctk.CTkLabel(
            dates_frame,
            text="التواريخ والميزانية",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        dates_title.pack(pady=(15, 10))
        
        # إطار التواريخ
        dates_row = ctk.CTkFrame(dates_frame, fg_color="transparent")
        dates_row.pack(fill="x", padx=20, pady=(0, 15))
        
        # تاريخ البداية
        start_frame = ctk.CTkFrame(dates_row, fg_color="transparent")
        start_frame.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        start_label = ctk.CTkLabel(start_frame, text="تاريخ البداية:")
        start_label.pack(anchor="w")
        
        self.start_date = DateEntry(
            start_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy/mm/dd'
        )
        self.start_date.pack(fill="x", pady=(5, 0))
        
        # تاريخ النهاية
        end_frame = ctk.CTkFrame(dates_row, fg_color="transparent")
        end_frame.pack(side="right", fill="x", expand=True, padx=(10, 0))
        
        end_label = ctk.CTkLabel(end_frame, text="تاريخ النهاية:")
        end_label.pack(anchor="w")
        
        self.end_date = DateEntry(
            end_frame,
            width=12,
            background='darkblue',
            foreground='white',
            borderwidth=2,
            date_pattern='yyyy/mm/dd'
        )
        self.end_date.pack(fill="x", pady=(5, 0))
        
        # إطار الميزانية
        budget_row = ctk.CTkFrame(dates_frame, fg_color="transparent")
        budget_row.pack(fill="x", padx=20, pady=(0, 20))
        
        # الميزانية المخططة
        planned_frame = ctk.CTkFrame(budget_row, fg_color="transparent")
        planned_frame.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        planned_label = ctk.CTkLabel(planned_frame, text="الميزانية المخططة (ريال):")
        planned_label.pack(anchor="w")
        
        self.planned_budget_entry = ctk.CTkEntry(
            planned_frame,
            textvariable=self.planned_budget_var,
            placeholder_text="0",
            height=35
        )
        self.planned_budget_entry.pack(fill="x", pady=(5, 0))
        
        # الميزانية الفعلية
        actual_frame = ctk.CTkFrame(budget_row, fg_color="transparent")
        actual_frame.pack(side="right", fill="x", expand=True, padx=(10, 0))
        
        actual_label = ctk.CTkLabel(actual_frame, text="الميزانية الفعلية (ريال):")
        actual_label.pack(anchor="w")
        
        self.actual_budget_entry = ctk.CTkEntry(
            actual_frame,
            textvariable=self.actual_budget_var,
            placeholder_text="0",
            height=35
        )
        self.actual_budget_entry.pack(fill="x", pady=(5, 0))
        
        # إدارة المشروع
        management_frame = ctk.CTkFrame(scroll_frame)
        management_frame.pack(fill="x", pady=(0, 20))
        
        management_title = ctk.CTkLabel(
            management_frame,
            text="إدارة المشروع",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        management_title.pack(pady=(15, 10))
        
        # مدير المشروع
        manager_label = ctk.CTkLabel(management_frame, text="مدير المشروع:")
        manager_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.manager_combo = ctk.CTkComboBox(
            management_frame,
            variable=self.manager_var,
            values=["اختر مدير المشروع..."],
            height=35,
            state="readonly"
        )
        self.manager_combo.pack(fill="x", padx=20, pady=(0, 15))
        
        # المقاول
        contractor_label = ctk.CTkLabel(management_frame, text="المقاول:")
        contractor_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.contractor_combo = ctk.CTkComboBox(
            management_frame,
            variable=self.contractor_var,
            values=["اختر المقاول..."],
            height=35,
            state="readonly"
        )
        self.contractor_combo.pack(fill="x", padx=20, pady=(0, 20))
        
        # حالة المشروع
        status_frame = ctk.CTkFrame(scroll_frame)
        status_frame.pack(fill="x", pady=(0, 20))
        
        status_title = ctk.CTkLabel(
            status_frame,
            text="حالة المشروع",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(15, 10))
        
        # إطار الحالة والأولوية
        status_row = ctk.CTkFrame(status_frame, fg_color="transparent")
        status_row.pack(fill="x", padx=20, pady=(0, 15))
        
        # الحالة
        status_left = ctk.CTkFrame(status_row, fg_color="transparent")
        status_left.pack(side="left", fill="x", expand=True, padx=(0, 10))
        
        status_label = ctk.CTkLabel(status_left, text="الحالة:")
        status_label.pack(anchor="w")
        
        statuses = ["Planned", "InProgress", "OnHold", "Completed", "Cancelled"]
        status_names = ["مخطط", "قيد التنفيذ", "متوقف", "مكتمل", "ملغي"]
        
        self.status_combo = ctk.CTkComboBox(
            status_left,
            variable=self.status_var,
            values=statuses,
            height=35,
            state="readonly"
        )
        self.status_combo.pack(fill="x", pady=(5, 0))
        
        # الأولوية
        priority_right = ctk.CTkFrame(status_row, fg_color="transparent")
        priority_right.pack(side="right", fill="x", expand=True, padx=(10, 0))
        
        priority_label = ctk.CTkLabel(priority_right, text="الأولوية:")
        priority_label.pack(anchor="w")
        
        priorities = ["Low", "Medium", "High", "Critical"]
        priority_names = ["منخفضة", "متوسطة", "عالية", "حرجة"]
        
        self.priority_combo = ctk.CTkComboBox(
            priority_right,
            variable=self.priority_var,
            values=priorities,
            height=35,
            state="readonly"
        )
        self.priority_combo.pack(fill="x", pady=(5, 0))
        
        # نسبة الإنجاز
        completion_label = ctk.CTkLabel(status_frame, text="نسبة الإنجاز (%):")
        completion_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.completion_entry = ctk.CTkEntry(
            status_frame,
            textvariable=self.completion_var,
            placeholder_text="0",
            height=35
        )
        self.completion_entry.pack(fill="x", padx=20, pady=(0, 20))
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(self.window)
        buttons_frame.pack(fill="x", padx=20, pady=20)
        
        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_project,
            width=120,
            height=40,
            fg_color="#27ae60",
            hover_color="#229954"
        )
        save_button.pack(side="right", padx=5)
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            width=120,
            height=40,
            fg_color="#95a5a6",
            hover_color="#7f8c8d"
        )
        cancel_button.pack(side="right", padx=5)
        
        # تركيز على أول حقل
        self.name_entry.focus()
    
    def load_data(self):
        """تحميل البيانات المساعدة"""
        # تحميل الموظفين
        employees = self.db_manager.get_employees()
        if employees:
            self.employees_data = employees
            employee_names = ["بدون مدير"] + [f"{emp[1]} - {emp[2]}" for emp in employees]
            self.manager_combo.configure(values=employee_names)
            self.manager_combo.set("بدون مدير")
        
        # تحميل المقاولين (سيتم تطوير هذا لاحقاً)
        contractor_names = ["بدون مقاول", "مقاول أ", "مقاول ب", "مقاول ج"]
        self.contractor_combo.configure(values=contractor_names)
        self.contractor_combo.set("بدون مقاول")
        
        # تعيين القيم الافتراضية
        self.status_var.set("Planned")
        self.priority_var.set("Medium")
        self.completion_var.set("0")
    
    def load_project_data(self):
        """تحميل بيانات المشروع للتعديل"""
        query = """
            SELECT ProjectName, ProjectDescription, ProjectType, Location, StartDate, EndDate,
                   PlannedBudget, ActualBudget, ProjectManager, ContractorID, Status, Priority,
                   CompletionPercentage
            FROM Projects
            WHERE ProjectID = ?
        """
        
        result = self.db_manager.execute_query(query, (self.project_id,))
        
        if result and len(result) > 0:
            project_data = result[0]
            
            # تعبئة الحقول
            self.project_name_var.set(project_data[0] or "")
            self.desc_text.insert("1.0", project_data[1] or "")
            self.project_type_var.set(project_data[2] or "")
            self.location_var.set(project_data[3] or "")
            
            # التواريخ
            if project_data[4]:  # تاريخ البداية
                self.start_date.set_date(project_data[4])
            if project_data[5]:  # تاريخ النهاية
                self.end_date.set_date(project_data[5])
            
            # الميزانية
            self.planned_budget_var.set(str(project_data[6] or 0))
            self.actual_budget_var.set(str(project_data[7] or 0))
            
            # مدير المشروع
            if project_data[8]:
                for emp in self.employees_data:
                    if emp[0] == project_data[8]:
                        self.manager_var.set(f"{emp[1]} - {emp[2]}")
                        break
            
            # الحالة والأولوية
            self.status_var.set(project_data[10] or "Planned")
            self.priority_var.set(project_data[11] or "Medium")
            self.completion_var.set(str(project_data[12] or 0))
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.project_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
            self.name_entry.focus()
            return False
        
        # التحقق من الميزانية
        try:
            planned = float(self.planned_budget_var.get() or 0)
            actual = float(self.actual_budget_var.get() or 0)
            if planned < 0 or actual < 0:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للميزانية")
            return False
        
        # التحقق من نسبة الإنجاز
        try:
            completion = float(self.completion_var.get() or 0)
            if completion < 0 or completion > 100:
                raise ValueError()
        except ValueError:
            messagebox.showerror("خطأ", "نسبة الإنجاز يجب أن تكون بين 0 و 100")
            return False
        
        return True
    
    def save_project(self):
        """حفظ بيانات المشروع"""
        if not self.validate_form():
            return
        
        try:
            # تحديد معرف مدير المشروع
            manager_id = None
            if self.manager_var.get() != "بدون مدير":
                for emp in self.employees_data:
                    if f"{emp[1]} - {emp[2]}" == self.manager_var.get():
                        manager_id = emp[0]
                        break
            
            # إعداد البيانات
            project_data = {
                'name': self.project_name_var.get().strip(),
                'description': self.desc_text.get("1.0", tk.END).strip(),
                'type': self.project_type_var.get(),
                'location': self.location_var.get().strip(),
                'start_date': self.start_date.get_date(),
                'end_date': self.end_date.get_date(),
                'budget': float(self.planned_budget_var.get() or 0),
                'actual_budget': float(self.actual_budget_var.get() or 0),
                'manager_id': manager_id,
                'status': self.status_var.get(),
                'priority': self.priority_var.get(),
                'completion': float(self.completion_var.get() or 0),
                'created_by': self.current_user['user_id']
            }
            
            # حفظ البيانات
            if self.is_edit_mode:
                success = self.db_manager.update_project(self.project_id, project_data)
                action = "تحديث"
            else:
                success = self.db_manager.add_project(project_data)
                action = "إضافة"
            
            if success:
                messagebox.showinfo("نجح", f"تم {action} المشروع بنجاح")
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", f"فشل في {action} المشروع")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.window.destroy()
