#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشاريع الهندسية - بلدية كفرنجة
Engineering Project Management System - Kufranja Municipality

Setup script for installing required packages and configuring the environment
"""

import subprocess
import sys
import os
from pathlib import Path

def install_requirements():
    """تثبيت المكتبات المطلوبة"""
    print("تثبيت المكتبات المطلوبة...")
    print("Installing required packages...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✓ تم تثبيت جميع المكتبات بنجاح")
        print("✓ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ خطأ في تثبيت المكتبات: {e}")
        print(f"✗ Error installing packages: {e}")
        return False

def create_directories():
    """إنشاء المجلدات المطلوبة"""
    print("إنشاء المجلدات المطلوبة...")
    print("Creating required directories...")
    
    directories = [
        "database",
        "reports",
        "templates",
        "attachments",
        "logs",
        "config",
        "backup"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✓ تم إنشاء مجلد: {directory}")
        print(f"✓ Created directory: {directory}")

def create_config_file():
    """إنشاء ملف الإعدادات الافتراضي"""
    config_content = """[DATABASE]
driver = Microsoft Access Driver (*.mdb, *.accdb)
database_path = database/kufranja_projects.accdb
backup_interval = 24

[APPLICATION]
app_name = نظام إدارة المشاريع الهندسية - بلدية كفرنجة
version = 1.0.0
language = ar
theme = modern

[REPORTS]
default_template = standard
output_directory = reports
auto_backup = true

[SECURITY]
session_timeout = 30
password_min_length = 8
max_login_attempts = 3

[LOGGING]
log_level = INFO
log_file = logs/application.log
max_log_size = 10MB
"""
    
    config_path = Path("config/settings.ini")
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print("✓ تم إنشاء ملف الإعدادات")
    print("✓ Configuration file created")

def check_python_version():
    """فحص إصدار Python"""
    if sys.version_info < (3, 8):
        print("✗ يتطلب Python 3.8 أو أحدث")
        print("✗ Python 3.8 or newer is required")
        return False
    
    print(f"✓ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def main():
    """الدالة الرئيسية للإعداد"""
    print("=" * 60)
    print("نظام إدارة المشاريع الهندسية - بلدية كفرنجة")
    print("Engineering Project Management System - Kufranja Municipality")
    print("=" * 60)
    print()
    
    # فحص إصدار Python
    if not check_python_version():
        return False
    
    # إنشاء المجلدات
    create_directories()
    
    # إنشاء ملف الإعدادات
    create_config_file()
    
    # تثبيت المكتبات
    if not install_requirements():
        return False
    
    print()
    print("=" * 60)
    print("✓ تم إعداد البيئة بنجاح!")
    print("✓ Environment setup completed successfully!")
    print()
    print("لتشغيل البرنامج، استخدم الأمر:")
    print("To run the application, use:")
    print("python main.py")
    print("=" * 60)
    
    return True

if __name__ == "__main__":
    main()
