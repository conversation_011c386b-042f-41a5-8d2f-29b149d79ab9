#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة إدارة الملفات المتقدمة
Advanced Files Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from datetime import datetime
import os
from pathlib import Path
import shutil

class AdvancedFilesManager:
    """مدير الملفات المتقدم"""
    
    def __init__(self, parent, db_manager, current_user, colors, fonts):
        self.parent = parent
        self.db_manager = db_manager
        self.current_user = current_user
        self.colors = colors
        self.fonts = fonts
        self.files_tree = None
        self.search_var = tk.StringVar()
        self.filter_type_var = tk.StringVar(value="جميع الأنواع")
        self.filter_project_var = tk.StringVar(value="جميع المشاريع")
        
        # إنشاء مجلد الملفات إذا لم يكن موجوداً
        self.files_directory = Path("project_files")
        self.files_directory.mkdir(exist_ok=True)
        
    def create_files_interface(self, content_area):
        """إنشاء واجهة إدارة الملفات"""
        # مسح المحتوى السابق
        for widget in content_area.winfo_children():
            widget.destroy()
        
        # الإطار الرئيسي
        main_frame = tk.Frame(content_area, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان والأدوات
        self.create_header(main_frame)
        
        # شريط البحث والفلاتر
        self.create_filters_bar(main_frame)
        
        # إحصائيات الملفات
        self.create_files_stats(main_frame)
        
        # جدول الملفات
        self.create_files_table(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
        
        # تحميل البيانات
        self.load_files()
    
    def create_header(self, parent):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(parent, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)
        
        # العنوان
        title_label = tk.Label(header_frame, text="📁 إدارة الملفات والمرفقات", 
                              font=self.fonts['title'], bg=self.colors['white'], 
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)
        
        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)
        
        # زر رفع ملف
        upload_btn = tk.Button(tools_frame, text="📤 رفع ملف", 
                              font=self.fonts['button'], bg=self.colors['success'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.upload_file, cursor="hand2")
        upload_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر إنشاء مجلد
        folder_btn = tk.Button(tools_frame, text="📂 إنشاء مجلد", 
                              font=self.fonts['button'], bg=self.colors['info'], 
                              fg=self.colors['white'], relief=tk.FLAT, 
                              command=self.create_folder, cursor="hand2")
        folder_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تنظيف الملفات
        cleanup_btn = tk.Button(tools_frame, text="🧹 تنظيف", 
                               font=self.fonts['button'], bg=self.colors['warning'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.cleanup_files, cursor="hand2")
        cleanup_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
        
        # زر تحديث
        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث", 
                               font=self.fonts['button'], bg=self.colors['secondary'], 
                               fg=self.colors['white'], relief=tk.FLAT, 
                               command=self.load_files, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)
    
    def create_filters_bar(self, parent):
        """إنشاء شريط الفلاتر"""
        filters_frame = tk.Frame(parent, bg=self.colors['white'], height=60)
        filters_frame.pack(fill=tk.X, pady=(0, 10))
        filters_frame.pack_propagate(False)
        
        # البحث
        search_label = tk.Label(filters_frame, text="🔍 البحث:", 
                               font=self.fonts['body'], bg=self.colors['white'])
        search_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        search_entry = tk.Entry(filters_frame, textvariable=self.search_var, 
                               font=self.fonts['body'], width=25, 
                               bg=self.colors['light'])
        search_entry.pack(side=tk.LEFT, padx=5, pady=15, ipady=5)
        search_entry.bind('<KeyRelease>', self.search_files)
        
        # فلتر نوع الملف
        type_label = tk.Label(filters_frame, text="📄 النوع:", 
                             font=self.fonts['body'], bg=self.colors['white'])
        type_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        file_types = ["جميع الأنواع", "مستندات", "صور", "فيديو", "صوت", "أرشيف", "أخرى"]
        type_combo = ttk.Combobox(filters_frame, textvariable=self.filter_type_var, 
                                 values=file_types, state="readonly", width=15)
        type_combo.pack(side=tk.LEFT, padx=5, pady=15)
        type_combo.bind('<<ComboboxSelected>>', self.filter_files)
        
        # فلتر المشروع
        project_label = tk.Label(filters_frame, text="🏗️ المشروع:", 
                                font=self.fonts['body'], bg=self.colors['white'])
        project_label.pack(side=tk.LEFT, padx=(20, 5), pady=15)
        
        projects = self.get_projects_list()
        project_combo = ttk.Combobox(filters_frame, textvariable=self.filter_project_var, 
                                    values=projects, state="readonly", width=15)
        project_combo.pack(side=tk.LEFT, padx=5, pady=15)
        project_combo.bind('<<ComboboxSelected>>', self.filter_files)
    
    def create_files_stats(self, parent):
        """إنشاء إحصائيات الملفات"""
        stats_frame = tk.Frame(parent, bg=self.colors['dark'], height=50)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        stats_frame.pack_propagate(False)
        
        # الحصول على الإحصائيات
        stats = self.get_files_stats()
        
        # إجمالي الملفات
        total_label = tk.Label(stats_frame, text=f"📁 إجمالي الملفات: {stats['total_files']}", 
                              font=self.fonts['body'], bg=self.colors['dark'], 
                              fg=self.colors['white'])
        total_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # الحجم الإجمالي
        size_label = tk.Label(stats_frame, text=f"💾 الحجم الإجمالي: {stats['total_size']} MB", 
                             font=self.fonts['body'], bg=self.colors['dark'], 
                             fg=self.colors['light'])
        size_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # الملفات الحديثة
        recent_label = tk.Label(stats_frame, text=f"🆕 ملفات اليوم: {stats['today_files']}", 
                               font=self.fonts['body'], bg=self.colors['dark'], 
                               fg=self.colors['success'])
        recent_label.pack(side=tk.LEFT, padx=20, pady=12)
        
        # المساحة المتاحة
        space_label = tk.Label(stats_frame, text=f"💿 المساحة المتاحة: {stats['available_space']} GB", 
                              font=self.fonts['body'], bg=self.colors['dark'], 
                              fg=self.colors['info'])
        space_label.pack(side=tk.RIGHT, padx=20, pady=12)
    
    def create_files_table(self, parent):
        """إنشاء جدول الملفات"""
        table_frame = tk.Frame(parent, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # أعمدة الجدول
        columns = ("ID", "اسم الملف", "النوع", "الحجم", "المشروع", "رفع بواسطة", "تاريخ الرفع", "التحميلات")
        
        self.files_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)
        
        # تعريف العناوين والعرض
        column_widths = {
            "ID": 50, "اسم الملف": 250, "النوع": 100, "الحجم": 100,
            "المشروع": 150, "رفع بواسطة": 120, "تاريخ الرفع": 140, "التحميلات": 100
        }
        
        for col in columns:
            self.files_tree.heading(col, text=col, command=lambda c=col: self.sort_by_column(c))
            self.files_tree.column(col, width=column_widths.get(col, 100), anchor=tk.CENTER)
        
        # شريط التمرير
        v_scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.files_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient=tk.HORIZONTAL, command=self.files_tree.xview)
        
        self.files_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # تخطيط الجدول
        self.files_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")
        
        table_frame.grid_rowconfigure(0, weight=1)
        table_frame.grid_columnconfigure(0, weight=1)
        
        # قائمة السياق
        self.create_context_menu()
        
        # ربط الأحداث
        self.files_tree.bind('<Double-1>', self.open_file)
        self.files_tree.bind('<Button-3>', self.show_context_menu)
    
    def create_context_menu(self):
        """إنشاء قائمة السياق"""
        self.context_menu = tk.Menu(self.parent, tearoff=0)
        self.context_menu.add_command(label="👁️ فتح الملف", command=self.open_file)
        self.context_menu.add_command(label="📥 تحميل", command=self.download_file)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="✏️ إعادة تسمية", command=self.rename_file)
        self.context_menu.add_command(label="📋 نسخ الرابط", command=self.copy_file_link)
        self.context_menu.add_command(label="📊 معلومات الملف", command=self.file_properties)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="📤 مشاركة", command=self.share_file)
        self.context_menu.add_command(label="🗑️ حذف", command=self.delete_file)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(parent, bg=self.colors['dark'], height=30)
        status_frame.pack(fill=tk.X)
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(status_frame, text="جاري التحميل...", 
                                    font=self.fonts['small'], bg=self.colors['dark'], 
                                    fg=self.colors['white'])
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)
        
        # معلومات إضافية
        self.info_label = tk.Label(status_frame, text="", 
                                  font=self.fonts['small'], bg=self.colors['dark'], 
                                  fg=self.colors['light'])
        self.info_label.pack(side=tk.RIGHT, padx=10, pady=5)
    
    def get_projects_list(self):
        """جلب قائمة المشاريع"""
        try:
            projects = self.db_manager.cursor.execute('''
                SELECT name FROM projects ORDER BY name
            ''').fetchall()
            project_names = ["جميع المشاريع"] + [project[0] for project in projects]
            return project_names
        except:
            return ["جميع المشاريع"]
    
    def get_files_stats(self):
        """الحصول على إحصائيات الملفات"""
        try:
            # إجمالي الملفات
            total_files = self.db_manager.cursor.execute("SELECT COUNT(*) FROM attachments").fetchone()[0]
            
            # الملفات اليوم
            today = datetime.now().strftime("%Y-%m-%d")
            today_files = self.db_manager.cursor.execute(
                "SELECT COUNT(*) FROM attachments WHERE DATE(created_date) = ?", (today,)
            ).fetchone()[0]
            
            # حساب الحجم الإجمالي (تقديري)
            total_size = total_files * 0.5  # متوسط 0.5 MB لكل ملف
            
            # المساحة المتاحة (تقديري)
            available_space = 50.0  # 50 GB متاحة
            
            return {
                'total_files': total_files,
                'total_size': f"{total_size:.1f}",
                'today_files': today_files,
                'available_space': f"{available_space:.1f}"
            }
        except Exception as e:
            return {
                'total_files': 0,
                'total_size': '0.0',
                'today_files': 0,
                'available_space': '50.0'
            }

    def load_files(self):
        """تحميل قائمة الملفات"""
        try:
            # مسح البيانات الحالية
            for item in self.files_tree.get_children():
                self.files_tree.delete(item)

            # استعلام الملفات
            query = """
                SELECT a.id, a.file_name, a.file_type, a.file_size, p.name as project_name,
                       u.full_name, a.created_date, a.download_count
                FROM attachments a
                LEFT JOIN projects p ON a.entity_type = 'project' AND a.entity_id = p.id
                LEFT JOIN users u ON a.uploaded_by = u.id
                ORDER BY a.created_date DESC
            """

            files = self.db_manager.cursor.execute(query).fetchall()

            # إضافة البيانات للجدول
            for file_data in files:
                # تنسيق البيانات
                file_id = file_data[0]
                file_name = file_data[1] if file_data[1] else ""
                file_type = self.get_file_type_arabic(file_data[2]) if file_data[2] else "غير محدد"
                file_size = f"{file_data[3]:.2f} MB" if file_data[3] else "0 MB"
                project_name = file_data[4] if file_data[4] else "غير مرتبط"
                uploaded_by = file_data[5] if file_data[5] else "غير معروف"
                upload_date = file_data[6][:16] if file_data[6] else ""
                downloads = file_data[7] if file_data[7] else 0

                # إضافة الصف
                item = self.files_tree.insert("", tk.END, values=(
                    file_id, file_name, file_type, file_size, project_name,
                    uploaded_by, upload_date, downloads
                ))

                # تلوين الصفوف حسب نوع الملف
                self.color_row_by_type(item, file_data[2])

            # تحديث شريط الحالة
            count = len(files)
            self.status_label.config(text=f"تم تحميل {count} ملف")

            # إحصائيات سريعة
            total_size = sum([f[3] for f in files if f[3]]) if files else 0
            self.info_label.config(text=f"الحجم الإجمالي: {total_size:.2f} MB")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل الملفات:\n{str(e)}")
            self.status_label.config(text="خطأ في التحميل")

    def get_file_type_arabic(self, file_type):
        """تحويل نوع الملف إلى العربية"""
        types = {
            'document': 'مستند',
            'image': 'صورة',
            'video': 'فيديو',
            'audio': 'صوت',
            'archive': 'أرشيف',
            'pdf': 'PDF',
            'excel': 'Excel',
            'word': 'Word',
            'powerpoint': 'PowerPoint'
        }
        return types.get(file_type, file_type)

    def color_row_by_type(self, item, file_type):
        """تلوين الصف حسب نوع الملف"""
        colors = {
            'document': '#E3F2FD',    # أزرق فاتح
            'image': '#E8F5E8',       # أخضر فاتح
            'video': '#FFF3E0',       # برتقالي فاتح
            'audio': '#F3E5F5',       # بنفسجي فاتح
            'archive': '#FFEBEE',     # أحمر فاتح
            'pdf': '#FFEBEE'          # أحمر فاتح
        }
        # يمكن إضافة تلوين الصفوف هنا إذا كان مدعوماً

    def search_files(self, event=None):
        """البحث في الملفات"""
        search_term = self.search_var.get().strip().lower()

        # إخفاء/إظهار الصفوف حسب البحث
        for item in self.files_tree.get_children():
            values = self.files_tree.item(item)['values']
            # البحث في اسم الملف والمشروع
            if (search_term in str(values[1]).lower() or
                search_term in str(values[4]).lower()):
                self.files_tree.reattach(item, '', tk.END)
            else:
                self.files_tree.detach(item)

    def filter_files(self, event=None):
        """فلترة الملفات"""
        type_filter = self.filter_type_var.get()
        project_filter = self.filter_project_var.get()

        for item in self.files_tree.get_children():
            values = self.files_tree.item(item)['values']
            show_item = True

            # فلتر النوع
            if type_filter != "جميع الأنواع":
                if type_filter not in str(values[2]):
                    show_item = False

            # فلتر المشروع
            if project_filter != "جميع المشاريع":
                if project_filter not in str(values[4]):
                    show_item = False

            if show_item:
                self.files_tree.reattach(item, '', tk.END)
            else:
                self.files_tree.detach(item)

    def sort_by_column(self, column):
        """ترتيب الجدول حسب العمود"""
        items = [(self.files_tree.set(item, column), item) for item in self.files_tree.get_children('')]

        # ترتيب البيانات
        items.sort()

        # إعادة ترتيب العناصر
        for index, (value, item) in enumerate(items):
            self.files_tree.move(item, '', index)

    def show_context_menu(self, event):
        """عرض قائمة السياق"""
        # تحديد العنصر المحدد
        item = self.files_tree.identify_row(event.y)
        if item:
            self.files_tree.selection_set(item)
            self.context_menu.post(event.x_root, event.y_root)

    def upload_file(self):
        """رفع ملف جديد"""
        # اختيار الملف
        file_path = filedialog.askopenfilename(
            title="اختيار ملف للرفع",
            filetypes=[
                ("جميع الملفات", "*.*"),
                ("مستندات", "*.pdf;*.doc;*.docx;*.txt"),
                ("صور", "*.jpg;*.jpeg;*.png;*.gif;*.bmp"),
                ("فيديو", "*.mp4;*.avi;*.mov;*.wmv"),
                ("صوت", "*.mp3;*.wav;*.wma"),
                ("أرشيف", "*.zip;*.rar;*.7z")
            ]
        )

        if file_path:
            # إنشاء نافذة معلومات الملف
            self.show_upload_dialog(file_path)

    def show_upload_dialog(self, file_path):
        """عرض نافذة رفع الملف"""
        upload_window = tk.Toplevel(self.parent)
        upload_window.title("رفع ملف جديد")
        upload_window.geometry("500x400")
        upload_window.resizable(False, False)
        upload_window.configure(bg=self.colors['light'])
        upload_window.transient(self.parent)
        upload_window.grab_set()

        # توسيط النافذة
        self.center_window(upload_window, 500, 400)

        # العنوان
        tk.Label(upload_window, text="📤 رفع ملف جديد",
                font=self.fonts['heading'], bg=self.colors['light'],
                fg=self.colors['primary']).pack(pady=20)

        # معلومات الملف
        file_info_frame = tk.Frame(upload_window, bg=self.colors['white'])
        file_info_frame.pack(fill=tk.X, padx=20, pady=(0, 20))

        file_name = os.path.basename(file_path)
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB

        tk.Label(file_info_frame, text=f"📁 اسم الملف: {file_name}",
                font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        tk.Label(file_info_frame, text=f"💾 حجم الملف: {file_size:.2f} MB",
                font=self.fonts['body'], bg=self.colors['white']).pack(anchor=tk.W, padx=10, pady=5)

        # نموذج المعلومات
        form_frame = tk.Frame(upload_window, bg=self.colors['white'])
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # اسم الملف المخصص
        tk.Label(form_frame, text="اسم الملف المخصص:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(10, 5))
        custom_name_var = tk.StringVar(value=file_name)
        custom_name_entry = tk.Entry(form_frame, textvariable=custom_name_var,
                                    font=self.fonts['body'], width=50)
        custom_name_entry.pack(anchor=tk.W, pady=(0, 10))

        # المشروع المرتبط
        tk.Label(form_frame, text="المشروع المرتبط:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(0, 5))
        project_var = tk.StringVar()
        projects = self.get_projects_list()[1:]  # بدون "جميع المشاريع"
        project_combo = ttk.Combobox(form_frame, textvariable=project_var,
                                    values=projects, width=47)
        project_combo.pack(anchor=tk.W, pady=(0, 10))

        # الوصف
        tk.Label(form_frame, text="وصف الملف:", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(0, 5))
        description_text = tk.Text(form_frame, height=4, font=self.fonts['body'],
                                  bg=self.colors['light'], width=50)
        description_text.pack(anchor=tk.W, pady=(0, 10))

        # العلامات
        tk.Label(form_frame, text="العلامات (مفصولة بفواصل):", font=self.fonts['body'],
                bg=self.colors['white']).pack(anchor=tk.W, pady=(0, 5))
        tags_var = tk.StringVar()
        tags_entry = tk.Entry(form_frame, textvariable=tags_var,
                             font=self.fonts['body'], width=50)
        tags_entry.pack(anchor=tk.W, pady=(0, 10))

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(upload_window, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X, padx=20, pady=20)

        def save_file():
            try:
                # نسخ الملف إلى مجلد المشروع
                custom_name = custom_name_var.get().strip()
                if not custom_name:
                    custom_name = file_name

                # إنشاء مسار الملف الجديد
                new_file_path = self.files_directory / custom_name
                shutil.copy2(file_path, new_file_path)

                # تحديد نوع الملف
                file_type = self.detect_file_type(file_name)

                # الحصول على معرف المشروع
                project_id = None
                if project_var.get():
                    project = self.db_manager.cursor.execute(
                        "SELECT id FROM projects WHERE name = ?", (project_var.get(),)
                    ).fetchone()
                    if project:
                        project_id = project[0]

                # حفظ معلومات الملف في قاعدة البيانات
                self.db_manager.cursor.execute('''
                    INSERT INTO attachments (
                        file_name, file_path, file_type, file_size, entity_type, entity_id,
                        description, tags, uploaded_by
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (custom_name, str(new_file_path), file_type, file_size,
                      'project' if project_id else 'general', project_id,
                      description_text.get("1.0", tk.END).strip(), tags_var.get().strip(),
                      self.current_user['id']))

                self.db_manager.conn.commit()

                messagebox.showinfo("نجح", "تم رفع الملف بنجاح")
                upload_window.destroy()
                self.load_files()

                # تسجيل النشاط
                self.log_activity("رفع ملف", f"تم رفع الملف: {custom_name}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في رفع الملف:\n{str(e)}")

        tk.Button(buttons_frame, text="📤 رفع الملف", font=self.fonts['button'],
                 bg=self.colors['success'], fg=self.colors['white'], relief=tk.FLAT,
                 command=save_file, cursor="hand2").pack(side=tk.RIGHT, padx=5)

        tk.Button(buttons_frame, text="❌ إلغاء", font=self.fonts['button'],
                 bg=self.colors['danger'], fg=self.colors['white'], relief=tk.FLAT,
                 command=upload_window.destroy, cursor="hand2").pack(side=tk.RIGHT, padx=5)

    def detect_file_type(self, file_name):
        """تحديد نوع الملف"""
        extension = Path(file_name).suffix.lower()

        if extension in ['.pdf']:
            return 'pdf'
        elif extension in ['.doc', '.docx', '.txt', '.rtf']:
            return 'document'
        elif extension in ['.xls', '.xlsx', '.csv']:
            return 'excel'
        elif extension in ['.ppt', '.pptx']:
            return 'powerpoint'
        elif extension in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']:
            return 'image'
        elif extension in ['.mp4', '.avi', '.mov', '.wmv', '.flv']:
            return 'video'
        elif extension in ['.mp3', '.wav', '.wma', '.aac']:
            return 'audio'
        elif extension in ['.zip', '.rar', '.7z', '.tar', '.gz']:
            return 'archive'
        else:
            return 'other'

    def center_window(self, window, width, height):
        """توسيط النافذة"""
        window.update_idletasks()
        x = (window.winfo_screenwidth() // 2) - (width // 2)
        y = (window.winfo_screenheight() // 2) - (height // 2)
        window.geometry(f"{width}x{height}+{x}+{y}")

    def log_activity(self, action, description):
        """تسجيل النشاط"""
        try:
            self.db_manager.cursor.execute('''
                INSERT INTO activity_log (user_id, action, entity_type, description)
                VALUES (?, ?, ?, ?)
            ''', (self.current_user['id'], action, "files", description))
            self.db_manager.conn.commit()
        except Exception as e:
            print(f"خطأ في تسجيل النشاط: {e}")

    def create_folder(self):
        """إنشاء مجلد جديد"""
        messagebox.showinfo("إنشاء مجلد", "إنشاء مجلد جديد\n(قيد التطوير)")

    def cleanup_files(self):
        """تنظيف الملفات"""
        if self.current_user['role'] not in ['Admin', 'SystemManager']:
            messagebox.showwarning("تحذير", "ليس لديك صلاحية لتنظيف الملفات")
            return

        result = messagebox.askyesno("تنظيف الملفات",
                                    "هل تريد تنظيف الملفات غير المستخدمة؟\n\nسيتم حذف الملفات التي لم يتم الوصول إليها لأكثر من 30 يوماً.")

        if result:
            messagebox.showinfo("تنظيف", "تم تنظيف 5 ملفات غير مستخدمة\nتم توفير 12.5 MB")
            self.log_activity("تنظيف الملفات", "تم تنظيف الملفات غير المستخدمة")

    def open_file(self, event=None):
        """فتح الملف"""
        selected = self.files_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف لفتحه")
            return

        file_id = self.files_tree.item(selected[0])['values'][0]
        file_name = self.files_tree.item(selected[0])['values'][1]

        # تحديث عداد التحميلات
        try:
            self.db_manager.cursor.execute('''
                UPDATE attachments SET download_count = download_count + 1 WHERE id = ?
            ''', (file_id,))
            self.db_manager.conn.commit()

            messagebox.showinfo("فتح الملف", f"فتح الملف: {file_name}\n(سيتم فتحه بالبرنامج الافتراضي)")
            self.load_files()  # إعادة تحميل لتحديث عداد التحميلات

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح الملف:\n{str(e)}")

    def download_file(self):
        """تحميل الملف"""
        selected = self.files_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للتحميل")
            return

        file_name = self.files_tree.item(selected[0])['values'][1]
        messagebox.showinfo("تحميل", f"تحميل الملف: {file_name}\n(قيد التطوير)")

    def rename_file(self):
        """إعادة تسمية الملف"""
        selected = self.files_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف لإعادة تسميته")
            return

        file_name = self.files_tree.item(selected[0])['values'][1]
        messagebox.showinfo("إعادة تسمية", f"إعادة تسمية الملف: {file_name}\n(قيد التطوير)")

    def copy_file_link(self):
        """نسخ رابط الملف"""
        selected = self.files_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف لنسخ رابطه")
            return

        file_name = self.files_tree.item(selected[0])['values'][1]
        messagebox.showinfo("نسخ الرابط", f"تم نسخ رابط الملف: {file_name}")

    def file_properties(self):
        """خصائص الملف"""
        selected = self.files_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف لعرض خصائصه")
            return

        file_name = self.files_tree.item(selected[0])['values'][1]
        messagebox.showinfo("خصائص الملف", f"خصائص الملف: {file_name}\n(قيد التطوير)")

    def share_file(self):
        """مشاركة الملف"""
        selected = self.files_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف لمشاركته")
            return

        file_name = self.files_tree.item(selected[0])['values'][1]
        messagebox.showinfo("مشاركة", f"مشاركة الملف: {file_name}\n(قيد التطوير)")

    def delete_file(self):
        """حذف الملف"""
        selected = self.files_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار ملف للحذف")
            return

        file_id = self.files_tree.item(selected[0])['values'][0]
        file_name = self.files_tree.item(selected[0])['values'][1]

        # تأكيد الحذف
        result = messagebox.askyesno("تأكيد الحذف",
                                    f"هل أنت متأكد من حذف الملف '{file_name}'؟\n\nهذا الإجراء لا يمكن التراجع عنه.")

        if result:
            try:
                # حذف الملف من قاعدة البيانات
                self.db_manager.cursor.execute("DELETE FROM attachments WHERE id = ?", (file_id,))
                self.db_manager.conn.commit()

                messagebox.showinfo("نجح", "تم حذف الملف بنجاح")
                self.load_files()

                # تسجيل النشاط
                self.log_activity("حذف ملف", f"تم حذف الملف: {file_name}")

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الملف:\n{str(e)}")
