@echo off
chcp 65001 >nul
title نظام إدارة المشاريع الهندسية - بلدية كفرنجة

echo ========================================
echo نظام إدارة المشاريع الهندسية
echo بلدية كفرنجة الجديدة
echo ========================================
echo.

echo جاري فحص متطلبات النظام...
echo Checking system requirements...
echo.

REM فحص وجود Python
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Python غير مثبت على النظام
    echo Error: Python is not installed
    echo يرجى تثبيت Python 3.8 أو أحدث من: https://python.org
    echo Please install Python 3.8 or newer from: https://python.org
    pause
    exit /b 1
)

echo ✓ Python مثبت
echo ✓ Python installed
echo.

REM فحص وجود المكتبات المطلوبة
echo فحص المكتبات المطلوبة...
echo Checking required packages...

python -c "import tkinter, customtkinter, pyodbc, reportlab, openpyxl, docx, matplotlib, tkcalendar" >nul 2>&1
if %errorlevel% neq 0 (
    echo تحذير: بعض المكتبات غير مثبتة
    echo Warning: Some packages are not installed
    echo.
    echo هل تريد تثبيت المكتبات المطلوبة؟ (y/n)
    echo Do you want to install required packages? (y/n)
    set /p install_choice=
    
    if /i "%install_choice%"=="y" (
        echo جاري تثبيت المكتبات...
        echo Installing packages...
        pip install -r requirements.txt
        if %errorlevel% neq 0 (
            echo خطأ في تثبيت المكتبات
            echo Error installing packages
            pause
            exit /b 1
        )
        echo ✓ تم تثبيت المكتبات بنجاح
        echo ✓ Packages installed successfully
    ) else (
        echo تم إلغاء التثبيت
        echo Installation cancelled
        pause
        exit /b 1
    )
)

echo ✓ جميع المكتبات متوفرة
echo ✓ All packages available
echo.

REM فحص وجود قاعدة البيانات
if not exist "database\kufranja_projects.accdb" (
    echo قاعدة البيانات غير موجودة
    echo Database not found
    echo.
    echo هل تريد إنشاء قاعدة بيانات جديدة؟ (y/n)
    echo Do you want to create a new database? (y/n)
    set /p create_db=
    
    if /i "%create_db%"=="y" (
        echo جاري إنشاء قاعدة البيانات...
        echo Creating database...
        python create_database.py
        if %errorlevel% neq 0 (
            echo خطأ في إنشاء قاعدة البيانات
            echo Error creating database
            pause
            exit /b 1
        )
        echo ✓ تم إنشاء قاعدة البيانات بنجاح
        echo ✓ Database created successfully
    ) else (
        echo تم إلغاء إنشاء قاعدة البيانات
        echo Database creation cancelled
        pause
        exit /b 1
    )
)

echo ✓ قاعدة البيانات متوفرة
echo ✓ Database available
echo.

REM إنشاء المجلدات المطلوبة
if not exist "logs" mkdir logs
if not exist "reports" mkdir reports
if not exist "backup" mkdir backup
if not exist "attachments" mkdir attachments
if not exist "config" mkdir config

echo ✓ تم إنشاء المجلدات المطلوبة
echo ✓ Required directories created
echo.

echo ========================================
echo تشغيل البرنامج...
echo Starting application...
echo ========================================
echo.

echo بيانات الدخول الافتراضية:
echo Default login credentials:
echo اسم المستخدم / Username: admin
echo كلمة المرور / Password: admin123
echo.

REM تشغيل البرنامج
python main.py

