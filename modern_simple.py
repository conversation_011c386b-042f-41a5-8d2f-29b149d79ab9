#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة المشاريع الهندسية العصري المبسط - بلدية كفرنجة
Modern Simplified Engineering Project Management System - Kufranja Municipality

نسخة عصرية مبسطة تعمل مع tkinter الأساسي فقط
Modern simplified version that works with basic tkinter only
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
import hashlib
from datetime import datetime, date
import os
from pathlib import Path
import json

class ModernSimpleProjectManager:
    """نظام إدارة المشاريع العصري المبسط"""
    
    def __init__(self):
        """تهيئة النظام"""
        self.setup_directories()
        self.setup_database()
        self.setup_theme()
        self.current_user = None
        self.create_splash_screen()
    
    def setup_directories(self):
        """إعداد المجلدات المطلوبة"""
        directories = [
            "database", "attachments", "reports", "logs", 
            "config", "backup", "temp"
        ]
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
    
    def setup_database(self):
        """إعداد قاعدة البيانات"""
        self.db_path = "database/kufranja_modern_simple.db"
        self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
        self.cursor = self.conn.cursor()
        self.create_tables()
        self.insert_default_data()
    
    def setup_theme(self):
        """إعداد المظهر العصري"""
        self.colors = {
            'primary': '#2C3E50',      # أزرق داكن
            'secondary': '#3498DB',    # أزرق فاتح
            'success': '#27AE60',      # أخضر
            'warning': '#F39C12',      # برتقالي
            'danger': '#E74C3C',       # أحمر
            'light': '#ECF0F1',        # رمادي فاتح
            'dark': '#34495E',         # رمادي داكن
            'white': '#FFFFFF',        # أبيض
            'accent': '#9B59B6',       # بنفسجي
            'info': '#17A2B8'          # أزرق معلوماتي
        }
        
        self.fonts = {
            'title': ('Segoe UI', 20, 'bold'),
            'heading': ('Segoe UI', 16, 'bold'),
            'subheading': ('Segoe UI', 14, 'bold'),
            'body': ('Segoe UI', 12),
            'small': ('Segoe UI', 10),
            'button': ('Segoe UI', 11, 'bold')
        }
    
    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        
        # جدول المستخدمين المحدث
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                email TEXT,
                phone TEXT,
                role TEXT NOT NULL,
                department TEXT,
                avatar_path TEXT,
                is_active INTEGER DEFAULT 1,
                last_login TIMESTAMP,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by INTEGER,
                notes TEXT
            )
        ''')
        
        # جدول المشاريع المحدث
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS projects (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT,
                project_type TEXT,
                location TEXT,
                start_date DATE,
                end_date DATE,
                planned_budget REAL,
                actual_budget REAL DEFAULT 0,
                status TEXT DEFAULT 'مخطط',
                priority TEXT DEFAULT 'متوسط',
                completion_percentage REAL DEFAULT 0,
                project_manager INTEGER,
                contractor TEXT,
                tags TEXT,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (created_by) REFERENCES users (id),
                FOREIGN KEY (project_manager) REFERENCES users (id)
            )
        ''')
        
        # جدول المهام المحدث
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                project_id INTEGER NOT NULL,
                name TEXT NOT NULL,
                description TEXT,
                assigned_to INTEGER,
                start_date DATE,
                end_date DATE,
                estimated_hours REAL,
                actual_hours REAL DEFAULT 0,
                status TEXT DEFAULT 'غير مبدوء',
                priority TEXT DEFAULT 'متوسط',
                completion_percentage REAL DEFAULT 0,
                dependencies TEXT,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_modified TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                notes TEXT,
                FOREIGN KEY (project_id) REFERENCES projects (id),
                FOREIGN KEY (assigned_to) REFERENCES users (id),
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # جدول المرفقات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS attachments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_type TEXT NOT NULL,
                entity_id INTEGER NOT NULL,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                file_size INTEGER,
                file_type TEXT,
                uploaded_by INTEGER,
                upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                description TEXT,
                FOREIGN KEY (uploaded_by) REFERENCES users (id)
            )
        ''')
        
        # جدول التعليقات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS comments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                entity_type TEXT NOT NULL,
                entity_id INTEGER NOT NULL,
                user_id INTEGER NOT NULL,
                comment TEXT NOT NULL,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # جدول سجل النشاطات
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS activity_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER NOT NULL,
                action TEXT NOT NULL,
                entity_type TEXT,
                entity_id INTEGER,
                description TEXT,
                ip_address TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        self.conn.commit()
    
    def insert_default_data(self):
        """إدراج البيانات الافتراضية"""
        # التحقق من وجود المستخدم الافتراضي
        self.cursor.execute("SELECT COUNT(*) FROM users WHERE username = 'admin'")
        if self.cursor.fetchone()[0] == 0:
            # إضافة المستخدم الافتراضي
            hashed_password = hashlib.sha256("admin123".encode()).hexdigest()
            self.cursor.execute('''
                INSERT INTO users (username, password, full_name, email, role, department)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', ("admin", hashed_password, "مدير النظام", "<EMAIL>", "Admin", "تقنية المعلومات"))
            
            # إضافة مستخدمين تجريبيين
            users_data = [
                ("manager1", hashlib.sha256("manager123".encode()).hexdigest(), "أحمد محمد", "<EMAIL>", "ProjectManager", "الهندسة"),
                ("engineer1", hashlib.sha256("eng123".encode()).hexdigest(), "فاطمة علي", "<EMAIL>", "Employee", "الهندسة"),
                ("supervisor1", hashlib.sha256("super123".encode()).hexdigest(), "محمد خالد", "<EMAIL>", "SystemManager", "الإشراف")
            ]
            
            for user in users_data:
                self.cursor.execute('''
                    INSERT INTO users (username, password, full_name, email, role, department)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', user)
            
            # إضافة مشاريع تجريبية
            projects_data = [
                ("مشروع تطوير الطرق الرئيسية", "تطوير وصيانة الطرق الرئيسية في البلدية", "بنية تحتية", "كفرنجة", 500000, "قيد التنفيذ", "عالي", 65.5, 2),
                ("مشروع تحديث شبكة الإنارة", "تحديث وتطوير شبكة الإنارة العامة", "كهرباء", "المدينة", 250000, "مخطط", "متوسط", 0, 2),
                ("مشروع تطوير الحدائق العامة", "إنشاء وتطوير الحدائق والمساحات الخضراء", "بيئة", "الأحياء السكنية", 180000, "مكتمل", "منخفض", 100, 3)
            ]
            
            for project in projects_data:
                self.cursor.execute('''
                    INSERT INTO projects (name, description, project_type, location, planned_budget, status, priority, completion_percentage, project_manager, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', project + (1,))
            
            self.conn.commit()
    
    def create_splash_screen(self):
        """إنشاء شاشة البداية"""
        self.splash = tk.Tk()
        self.splash.title("نظام إدارة المشاريع الهندسية")
        self.splash.geometry("600x400")
        self.splash.resizable(False, False)
        self.splash.configure(bg=self.colors['primary'])
        
        # إزالة شريط العنوان
        self.splash.overrideredirect(True)
        
        # توسيط النافذة
        self.center_window(self.splash, 600, 400)
        
        # الإطار الرئيسي
        main_frame = tk.Frame(self.splash, bg=self.colors['primary'])
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # شعار البلدية (نص مؤقت)
        logo_frame = tk.Frame(main_frame, bg=self.colors['primary'])
        logo_frame.pack(expand=True)
        
        # أيقونة كبيرة
        icon_label = tk.Label(logo_frame, text="🏗️", font=('Arial', 80), 
                             bg=self.colors['primary'], fg=self.colors['white'])
        icon_label.pack(pady=(50, 20))
        
        # عنوان النظام
        title_label = tk.Label(logo_frame, text="نظام إدارة المشاريع الهندسية", 
                              font=self.fonts['title'], bg=self.colors['primary'], 
                              fg=self.colors['white'])
        title_label.pack(pady=(0, 10))
        
        # اسم البلدية
        subtitle_label = tk.Label(logo_frame, text="بلدية كفرنجة الجديدة", 
                                 font=self.fonts['heading'], bg=self.colors['primary'], 
                                 fg=self.colors['light'])
        subtitle_label.pack(pady=(0, 30))
        
        # شريط التحميل
        self.progress_var = tk.DoubleVar()
        progress_bar = ttk.Progressbar(logo_frame, variable=self.progress_var, 
                                      maximum=100, length=300, mode='determinate')
        progress_bar.pack(pady=(0, 20))
        
        # نص التحميل
        self.loading_label = tk.Label(logo_frame, text="جاري التحميل...", 
                                     font=self.fonts['body'], bg=self.colors['primary'], 
                                     fg=self.colors['light'])
        self.loading_label.pack()
        
        # معلومات الإصدار
        version_label = tk.Label(main_frame, text="الإصدار 2.0.0 | تطوير: فريق تقنية المعلومات", 
                                font=self.fonts['small'], bg=self.colors['primary'], 
                                fg=self.colors['light'])
        version_label.pack(side=tk.BOTTOM, pady=20)
        
        # بدء عملية التحميل
        self.splash.after(100, self.loading_animation)
    
    def loading_animation(self):
        """رسوم متحركة للتحميل"""
        steps = [
            (20, "تهيئة قاعدة البيانات..."),
            (40, "تحميل الإعدادات..."),
            (60, "فحص التحديثات..."),
            (80, "إعداد الواجهة..."),
            (100, "اكتمل التحميل!")
        ]
        
        current_step = 0
        
        def update_progress():
            nonlocal current_step
            if current_step < len(steps):
                progress, text = steps[current_step]
                self.progress_var.set(progress)
                self.loading_label.config(text=text)
                current_step += 1
                self.splash.after(800, update_progress)
            else:
                self.splash.after(1000, self.show_login)
        
        update_progress()
    
    def show_login(self):
        """عرض نافذة تسجيل الدخول"""
        self.splash.destroy()
        self.create_login_window()
    
    def center_window(self, window, width, height):
        """توسيط النافذة على الشاشة"""
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_login_window(self):
        """إنشاء نافذة تسجيل الدخول العصرية"""
        self.login_window = tk.Tk()
        self.login_window.title("تسجيل الدخول - نظام إدارة المشاريع الهندسية")
        self.login_window.geometry("500x650")
        self.login_window.resizable(False, False)
        self.login_window.configure(bg=self.colors['light'])

        # توسيط النافذة
        self.center_window(self.login_window, 500, 650)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.login_window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=30)

        # إطار الهيدر
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=120)
        header_frame.pack(fill=tk.X, pady=(0, 30))
        header_frame.pack_propagate(False)

        # أيقونة النظام
        icon_label = tk.Label(header_frame, text="🏗️", font=('Arial', 40),
                             bg=self.colors['primary'], fg=self.colors['white'])
        icon_label.pack(pady=(20, 5))

        # عنوان النظام
        title_label = tk.Label(header_frame, text="نظام إدارة المشاريع الهندسية",
                              font=self.fonts['heading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack()

        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        login_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # عنوان تسجيل الدخول
        login_title = tk.Label(login_frame, text="تسجيل الدخول",
                              font=self.fonts['subheading'], bg=self.colors['white'],
                              fg=self.colors['dark'])
        login_title.pack(pady=(30, 20))

        # حقل اسم المستخدم
        username_frame = tk.Frame(login_frame, bg=self.colors['white'])
        username_frame.pack(fill=tk.X, padx=30, pady=(0, 15))

        tk.Label(username_frame, text="👤 اسم المستخدم", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        self.username_entry = tk.Entry(username_frame, font=self.fonts['body'],
                                      relief=tk.FLAT, bd=5, bg=self.colors['light'])
        self.username_entry.pack(fill=tk.X, ipady=8)

        # حقل كلمة المرور
        password_frame = tk.Frame(login_frame, bg=self.colors['white'])
        password_frame.pack(fill=tk.X, padx=30, pady=(0, 25))

        tk.Label(password_frame, text="🔒 كلمة المرور", font=self.fonts['body'],
                bg=self.colors['white'], fg=self.colors['dark']).pack(anchor=tk.W, pady=(0, 5))

        self.password_entry = tk.Entry(password_frame, font=self.fonts['body'],
                                      show="*", relief=tk.FLAT, bd=5, bg=self.colors['light'])
        self.password_entry.pack(fill=tk.X, ipady=8)

        # زر تسجيل الدخول
        login_button = tk.Button(login_frame, text="🚀 تسجيل الدخول",
                                font=self.fonts['button'], bg=self.colors['secondary'],
                                fg=self.colors['white'], relief=tk.FLAT,
                                command=self.login, cursor="hand2")
        login_button.pack(fill=tk.X, padx=30, pady=(0, 20), ipady=12)

        # معلومات المستخدمين
        info_frame = tk.Frame(main_frame, bg=self.colors['light'])
        info_frame.pack(fill=tk.X)

        info_text = """المستخدمون المتاحون:
👨‍💼 admin / admin123 (مدير النظام)
👨‍🔧 manager1 / manager123 (مدير مشروع)
👩‍💻 engineer1 / eng123 (مهندس)
👨‍💼 supervisor1 / super123 (مشرف)"""

        info_label = tk.Label(info_frame, text=info_text, font=self.fonts['small'],
                             bg=self.colors['light'], fg=self.colors['dark'],
                             justify=tk.LEFT)
        info_label.pack(pady=10)

        # ربط مفتاح Enter
        self.login_window.bind('<Return>', lambda e: self.login())

        # تركيز على حقل اسم المستخدم
        self.username_entry.focus()

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def authenticate_user(self, username, password):
        """التحقق من صحة بيانات المستخدم"""
        hashed_password = self.hash_password(password)
        self.cursor.execute('''
            SELECT id, username, full_name, email, role, department, avatar_path
            FROM users
            WHERE username = ? AND password = ? AND is_active = 1
        ''', (username, hashed_password))

        result = self.cursor.fetchone()
        if result:
            user_data = {
                'id': result[0],
                'username': result[1],
                'full_name': result[2],
                'email': result[3],
                'role': result[4],
                'department': result[5],
                'avatar_path': result[6]
            }

            # تحديث وقت آخر دخول
            self.cursor.execute('''
                UPDATE users SET last_login = ? WHERE id = ?
            ''', (datetime.now(), user_data['id']))

            # تسجيل النشاط
            self.log_activity(user_data['id'], "تسجيل دخول", "users", user_data['id'],
                             f"تسجيل دخول المستخدم {user_data['full_name']}")

            self.conn.commit()
            return user_data

        return None

    def log_activity(self, user_id, action, entity_type=None, entity_id=None, description=None):
        """تسجيل نشاط المستخدم"""
        self.cursor.execute('''
            INSERT INTO activity_log (user_id, action, entity_type, entity_id, description)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, action, entity_type, entity_id, description))
        self.conn.commit()

    def login(self):
        """معالج تسجيل الدخول"""
        username = self.username_entry.get().strip()
        password = self.password_entry.get().strip()

        if not username or not password:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم وكلمة المرور")
            return

        user = self.authenticate_user(username, password)
        if user:
            self.current_user = user
            messagebox.showinfo("نجح تسجيل الدخول",
                               f"مرحباً {user['full_name']}\nالصلاحية: {self.get_role_name(user['role'])}")
            self.login_window.destroy()
            self.create_main_window()
        else:
            messagebox.showerror("خطأ", "اسم المستخدم أو كلمة المرور غير صحيحة")
            self.password_entry.delete(0, tk.END)

    def get_role_name(self, role):
        """الحصول على اسم الصلاحية بالعربية"""
        roles = {
            'Admin': 'مدير النظام',
            'ProjectManager': 'مدير مشروع',
            'Employee': 'موظف',
            'SystemManager': 'مشرف النظام'
        }
        return roles.get(role, role)

    def create_main_window(self):
        """إنشاء النافذة الرئيسية العصرية"""
        self.main_window = tk.Tk()
        self.main_window.title(f"نظام إدارة المشاريع الهندسية - {self.current_user['full_name']}")
        self.main_window.geometry("1400x900")
        self.main_window.state('zoomed')
        self.main_window.configure(bg=self.colors['light'])

        # إنشاء الشريط العلوي
        self.create_top_bar()

        # إنشاء الشريط الجانبي
        self.create_sidebar()

        # إنشاء المنطقة الرئيسية
        self.create_main_content_area()

        # تحميل لوحة التحكم افتراضياً
        self.show_dashboard()

        # إعداد الاختصارات
        self.setup_shortcuts()

    def create_top_bar(self):
        """إنشاء الشريط العلوي"""
        self.top_bar = tk.Frame(self.main_window, bg=self.colors['primary'], height=70)
        self.top_bar.pack(fill=tk.X)
        self.top_bar.pack_propagate(False)

        # الجانب الأيمن - معلومات النظام
        right_frame = tk.Frame(self.top_bar, bg=self.colors['primary'])
        right_frame.pack(side=tk.RIGHT, fill=tk.Y, padx=20)

        # عنوان النظام
        title_label = tk.Label(right_frame, text="🏗️ نظام إدارة المشاريع الهندسية",
                              font=self.fonts['heading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(side=tk.RIGHT, pady=15)

        # الجانب الأيسر - معلومات المستخدم والأدوات
        left_frame = tk.Frame(self.top_bar, bg=self.colors['primary'])
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=20)

        # معلومات المستخدم
        user_frame = tk.Frame(left_frame, bg=self.colors['primary'])
        user_frame.pack(side=tk.LEFT, pady=10)

        # صورة المستخدم (افتراضية)
        avatar_label = tk.Label(user_frame, text="👤", font=('Arial', 20),
                               bg=self.colors['secondary'], fg=self.colors['white'],
                               width=3, height=1)
        avatar_label.pack(side=tk.LEFT, padx=(0, 10))

        # اسم المستخدم والصلاحية
        user_info_frame = tk.Frame(user_frame, bg=self.colors['primary'])
        user_info_frame.pack(side=tk.LEFT)

        name_label = tk.Label(user_info_frame, text=f"مرحباً، {self.current_user['full_name']}",
                             font=self.fonts['body'], bg=self.colors['primary'],
                             fg=self.colors['white'])
        name_label.pack(anchor=tk.W)

        role_label = tk.Label(user_info_frame, text=f"{self.get_role_name(self.current_user['role'])} | {self.current_user['department']}",
                             font=self.fonts['small'], bg=self.colors['primary'],
                             fg=self.colors['light'])
        role_label.pack(anchor=tk.W)

        # أدوات سريعة
        tools_frame = tk.Frame(left_frame, bg=self.colors['primary'])
        tools_frame.pack(side=tk.LEFT, padx=(30, 0))

        # زر الإشعارات
        notif_button = tk.Button(tools_frame, text="🔔 3",
                                font=self.fonts['small'], bg=self.colors['warning'],
                                fg=self.colors['white'], relief=tk.FLAT,
                                command=self.show_notifications, cursor="hand2")
        notif_button.pack(side=tk.LEFT, padx=5, pady=15, ipadx=10)

        # زر البحث السريع
        search_button = tk.Button(tools_frame, text="🔍 بحث",
                                 font=self.fonts['small'], bg=self.colors['info'],
                                 fg=self.colors['white'], relief=tk.FLAT,
                                 command=self.quick_search, cursor="hand2")
        search_button.pack(side=tk.LEFT, padx=5, pady=15, ipadx=10)

        # زر تسجيل الخروج
        logout_button = tk.Button(tools_frame, text="🚪 خروج",
                                 font=self.fonts['small'], bg=self.colors['danger'],
                                 fg=self.colors['white'], relief=tk.FLAT,
                                 command=self.logout, cursor="hand2")
        logout_button.pack(side=tk.LEFT, padx=5, pady=15, ipadx=10)

    def show_notifications(self):
        """عرض الإشعارات"""
        messagebox.showinfo("الإشعارات", "لديك 3 إشعارات جديدة:\n• مشروع جديد تم إضافته\n• مهمة تحتاج موافقة\n• تقرير شهري جاهز")

    def quick_search(self):
        """البحث السريع"""
        search_window = tk.Toplevel(self.main_window)
        search_window.title("البحث السريع")
        search_window.geometry("400x300")
        search_window.transient(self.main_window)
        search_window.grab_set()

        tk.Label(search_window, text="🔍 البحث السريع", font=self.fonts['heading']).pack(pady=20)

        search_entry = tk.Entry(search_window, font=self.fonts['body'], width=40)
        search_entry.pack(pady=10)
        search_entry.focus()

        tk.Button(search_window, text="بحث", font=self.fonts['button'],
                 command=lambda: messagebox.showinfo("نتائج البحث", "لم يتم العثور على نتائج")).pack(pady=10)

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل أنت متأكد من تسجيل الخروج؟")
        if result:
            # تسجيل النشاط
            self.log_activity(self.current_user['id'], "تسجيل خروج", "users",
                             self.current_user['id'], f"تسجيل خروج المستخدم {self.current_user['full_name']}")

            self.main_window.destroy()
            self.current_user = None
            self.create_login_window()
            self.login_window.mainloop()

    def setup_shortcuts(self):
        """إعداد اختصارات لوحة المفاتيح"""
        self.main_window.bind('<Control-d>', lambda e: self.show_section('dashboard'))
        self.main_window.bind('<Control-p>', lambda e: self.show_section('projects'))
        self.main_window.bind('<Control-t>', lambda e: self.show_section('tasks'))
        self.main_window.bind('<Control-u>', lambda e: self.show_section('users'))
        self.main_window.bind('<Control-q>', lambda e: self.logout())
        self.main_window.bind('<F1>', lambda e: self.show_help())

    def show_help(self):
        """عرض المساعدة"""
        help_text = """🔧 اختصارات لوحة المفاتيح:

Ctrl+D - لوحة التحكم
Ctrl+P - إدارة المشاريع
Ctrl+T - إدارة المهام
Ctrl+U - إدارة المستخدمين
Ctrl+Q - تسجيل الخروج
F1 - المساعدة"""

        messagebox.showinfo("المساعدة", help_text)

    def run(self):
        """تشغيل التطبيق"""
        self.splash.mainloop()

    def create_sidebar(self):
        """إنشاء الشريط الجانبي"""
        self.sidebar = tk.Frame(self.main_window, bg=self.colors['dark'], width=280)
        self.sidebar.pack(side=tk.RIGHT, fill=tk.Y)
        self.sidebar.pack_propagate(False)

        # عنوان القائمة
        menu_title = tk.Label(self.sidebar, text="📋 القائمة الرئيسية",
                             font=self.fonts['subheading'], bg=self.colors['dark'],
                             fg=self.colors['white'])
        menu_title.pack(pady=(20, 30), padx=20)

        # قائمة الوظائف
        self.menu_items = [
            ("🏠", "لوحة التحكم", "dashboard", self.colors['secondary']),
            ("🏗️", "إدارة المشاريع", "projects", self.colors['success']),
            ("📋", "إدارة المهام", "tasks", self.colors['warning']),
            ("👥", "إدارة المستخدمين", "users", self.colors['info']),
            ("📊", "التقارير والإحصائيات", "reports", self.colors['accent']),
            ("📁", "إدارة الملفات", "files", self.colors['primary']),
            ("💬", "التعليقات والملاحظات", "comments", self.colors['secondary']),
            ("🔧", "الإعدادات", "settings", self.colors['dark'])
        ]

        self.menu_buttons = {}
        for icon, text, key, color in self.menu_items:
            button_frame = tk.Frame(self.sidebar, bg=self.colors['dark'])
            button_frame.pack(fill=tk.X, padx=15, pady=3)

            button = tk.Button(button_frame, text=f"{icon}  {text}",
                              font=self.fonts['body'], bg=self.colors['dark'],
                              fg=self.colors['white'], relief=tk.FLAT,
                              anchor=tk.W, command=lambda k=key: self.show_section(k),
                              cursor="hand2")
            button.pack(fill=tk.X, ipady=12, ipadx=20)

            # تأثير hover
            def on_enter(e, btn=button, clr=color):
                btn.config(bg=clr)

            def on_leave(e, btn=button):
                if btn != self.active_menu_button:
                    btn.config(bg=self.colors['dark'])

            button.bind("<Enter>", on_enter)
            button.bind("<Leave>", on_leave)

            self.menu_buttons[key] = button

        self.active_menu_button = None

        # معلومات النظام في الأسفل
        info_frame = tk.Frame(self.sidebar, bg=self.colors['dark'])
        info_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=20, pady=20)

        # إحصائيات سريعة
        stats_text = self.get_quick_stats()
        stats_label = tk.Label(info_frame, text=stats_text,
                              font=self.fonts['small'], bg=self.colors['dark'],
                              fg=self.colors['light'], justify=tk.LEFT)
        stats_label.pack()

        # معلومات الإصدار
        version_label = tk.Label(info_frame, text="الإصدار 2.0.0",
                                font=self.fonts['small'], bg=self.colors['dark'],
                                fg=self.colors['light'])
        version_label.pack(pady=(10, 0))

    def create_main_content_area(self):
        """إنشاء المنطقة الرئيسية للمحتوى"""
        self.content_area = tk.Frame(self.main_window, bg=self.colors['light'])
        self.content_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # إطار المحتوى الحالي
        self.current_content = None

    def get_quick_stats(self):
        """الحصول على إحصائيات سريعة"""
        try:
            self.cursor.execute("SELECT COUNT(*) FROM projects")
            total_projects = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'قيد التنفيذ'")
            active_projects = self.cursor.fetchone()[0]

            self.cursor.execute("SELECT COUNT(*) FROM users WHERE is_active = 1")
            active_users = self.cursor.fetchone()[0]

            return f"""📊 إحصائيات سريعة:
• المشاريع: {total_projects}
• النشطة: {active_projects}
• المستخدمين: {active_users}"""
        except:
            return "📊 إحصائيات سريعة:\n• جاري التحميل..."

    def show_section(self, section):
        """عرض قسم معين"""
        # تحديث الزر النشط
        if self.active_menu_button:
            self.active_menu_button.config(bg=self.colors['dark'])

        self.active_menu_button = self.menu_buttons[section]
        self.active_menu_button.config(bg=self.colors['secondary'])

        # مسح المحتوى الحالي
        if self.current_content:
            self.current_content.destroy()

        # عرض القسم المطلوب
        if section == "dashboard":
            self.show_dashboard()
        elif section == "projects":
            self.show_projects()
        elif section == "tasks":
            self.show_tasks()
        elif section == "users":
            self.show_users()
        elif section == "reports":
            self.show_reports()
        elif section == "files":
            self.show_files()
        elif section == "comments":
            self.show_comments()
        elif section == "settings":
            self.show_settings()

    def show_dashboard(self):
        """عرض لوحة التحكم"""
        self.current_content = tk.Frame(self.content_area, bg=self.colors['light'])
        self.current_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # عنوان لوحة التحكم
        header_frame = tk.Frame(self.current_content, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="🏠 لوحة التحكم",
                              font=self.fonts['title'], bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)

        # التاريخ والوقت
        current_time = datetime.now().strftime("%Y/%m/%d - %H:%M")
        time_label = tk.Label(header_frame, text=f"📅 {current_time}",
                             font=self.fonts['body'], bg=self.colors['white'],
                             fg=self.colors['dark'])
        time_label.pack(side=tk.RIGHT, pady=20, padx=20)

        # بطاقات الإحصائيات
        stats_frame = tk.Frame(self.current_content, bg=self.colors['light'])
        stats_frame.pack(fill=tk.X, pady=(0, 20))

        # الحصول على الإحصائيات
        stats = self.get_dashboard_stats()

        # إنشاء البطاقات
        cards_data = [
            ("إجمالي المشاريع", stats['total_projects'], "🏗️", self.colors['primary']),
            ("المشاريع النشطة", stats['active_projects'], "⚡", self.colors['success']),
            ("المشاريع المكتملة", stats['completed_projects'], "✅", self.colors['info']),
            ("إجمالي الميزانية", f"{stats['total_budget']:,.0f} ريال", "💰", self.colors['warning'])
        ]

        for i, (title, value, icon, color) in enumerate(cards_data):
            card = self.create_stat_card(stats_frame, title, value, icon, color)
            card.grid(row=0, column=i, padx=10, sticky="ew")
            stats_frame.grid_columnconfigure(i, weight=1)

        # المحتوى الرئيسي
        main_content_frame = tk.Frame(self.current_content, bg=self.colors['light'])
        main_content_frame.pack(fill=tk.BOTH, expand=True)

        # الجانب الأيسر - المشاريع الحديثة
        left_panel = tk.Frame(main_content_frame, bg=self.colors['white'])
        left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        self.create_recent_projects_panel(left_panel)

        # الجانب الأيمن - الأنشطة والإشعارات
        right_panel = tk.Frame(main_content_frame, bg=self.colors['white'], width=350)
        right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(10, 0))
        right_panel.pack_propagate(False)

        self.create_activity_panel(right_panel)

    def create_stat_card(self, parent, title, value, icon, color):
        """إنشاء بطاقة إحصائية"""
        card = tk.Frame(parent, bg=self.colors['white'], relief=tk.RAISED, bd=1)

        # الأيقونة والقيمة
        top_frame = tk.Frame(card, bg=color, height=60)
        top_frame.pack(fill=tk.X)
        top_frame.pack_propagate(False)

        icon_label = tk.Label(top_frame, text=icon, font=('Arial', 24),
                             bg=color, fg=self.colors['white'])
        icon_label.pack(side=tk.LEFT, pady=15, padx=20)

        value_label = tk.Label(top_frame, text=str(value), font=self.fonts['heading'],
                              bg=color, fg=self.colors['white'])
        value_label.pack(side=tk.RIGHT, pady=15, padx=20)

        # العنوان
        title_label = tk.Label(card, text=title, font=self.fonts['body'],
                              bg=self.colors['white'], fg=self.colors['dark'])
        title_label.pack(pady=15)

        return card

    def get_dashboard_stats(self):
        """الحصول على إحصائيات لوحة التحكم"""
        stats = {}

        try:
            # إجمالي المشاريع
            self.cursor.execute("SELECT COUNT(*) FROM projects")
            stats['total_projects'] = self.cursor.fetchone()[0]

            # المشاريع النشطة
            self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'قيد التنفيذ'")
            stats['active_projects'] = self.cursor.fetchone()[0]

            # المشاريع المكتملة
            self.cursor.execute("SELECT COUNT(*) FROM projects WHERE status = 'مكتمل'")
            stats['completed_projects'] = self.cursor.fetchone()[0]

            # إجمالي الميزانية
            self.cursor.execute("SELECT SUM(planned_budget) FROM projects")
            result = self.cursor.fetchone()[0]
            stats['total_budget'] = result if result else 0

        except Exception as e:
            print(f"خطأ في الحصول على الإحصائيات: {e}")
            stats = {'total_projects': 0, 'active_projects': 0, 'completed_projects': 0, 'total_budget': 0}

        return stats

    def create_recent_projects_panel(self, parent):
        """إنشاء لوحة المشاريع الحديثة"""
        # العنوان
        header_frame = tk.Frame(parent, bg=self.colors['primary'], height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="🏗️ المشاريع الحديثة",
                              font=self.fonts['subheading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(side=tk.LEFT, pady=12, padx=20)

        # زر عرض الكل
        view_all_btn = tk.Button(header_frame, text="عرض الكل →",
                                font=self.fonts['small'], bg=self.colors['secondary'],
                                fg=self.colors['white'], relief=tk.FLAT,
                                command=lambda: self.show_section('projects'), cursor="hand2")
        view_all_btn.pack(side=tk.RIGHT, pady=12, padx=20)

        # قائمة المشاريع
        projects_frame = tk.Frame(parent, bg=self.colors['white'])
        projects_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # الحصول على المشاريع الحديثة
        try:
            self.cursor.execute('''
                SELECT name, status, completion_percentage, planned_budget
                FROM projects
                ORDER BY created_date DESC
                LIMIT 5
            ''')
            projects = self.cursor.fetchall()

            if projects:
                for i, project in enumerate(projects):
                    self.create_project_item(projects_frame, project, i)
            else:
                no_projects_label = tk.Label(projects_frame, text="لا توجد مشاريع حالياً",
                                           font=self.fonts['body'], bg=self.colors['white'],
                                           fg=self.colors['dark'])
                no_projects_label.pack(expand=True)
        except Exception as e:
            error_label = tk.Label(projects_frame, text=f"خطأ في تحميل المشاريع: {e}",
                                 font=self.fonts['body'], bg=self.colors['white'],
                                 fg=self.colors['danger'])
            error_label.pack(expand=True)

    def create_project_item(self, parent, project, index):
        """إنشاء عنصر مشروع"""
        item_frame = tk.Frame(parent, bg=self.colors['light'] if index % 2 == 0 else self.colors['white'])
        item_frame.pack(fill=tk.X, pady=2)

        # اسم المشروع
        name_label = tk.Label(item_frame, text=project[0][:40] + "..." if len(project[0]) > 40 else project[0],
                             font=self.fonts['body'], bg=item_frame.cget('bg'),
                             fg=self.colors['dark'])
        name_label.pack(anchor=tk.W, padx=15, pady=(10, 5))

        # معلومات إضافية
        info_frame = tk.Frame(item_frame, bg=item_frame.cget('bg'))
        info_frame.pack(fill=tk.X, padx=15, pady=(0, 10))

        # الحالة
        status_color = self.get_status_color(project[1])
        status_label = tk.Label(info_frame, text=f"● {project[1]}",
                               font=self.fonts['small'], bg=item_frame.cget('bg'),
                               fg=status_color)
        status_label.pack(side=tk.LEFT)

        # نسبة الإنجاز
        progress_label = tk.Label(info_frame, text=f"{project[2]:.1f}%",
                                 font=self.fonts['small'], bg=item_frame.cget('bg'),
                                 fg=self.colors['dark'])
        progress_label.pack(side=tk.RIGHT)

        # الميزانية
        budget_label = tk.Label(info_frame, text=f"{project[3]:,.0f} ريال",
                               font=self.fonts['small'], bg=item_frame.cget('bg'),
                               fg=self.colors['dark'])
        budget_label.pack(side=tk.RIGHT, padx=(0, 20))

    def get_status_color(self, status):
        """الحصول على لون الحالة"""
        colors = {
            'مخطط': self.colors['info'],
            'قيد التنفيذ': self.colors['warning'],
            'مكتمل': self.colors['success'],
            'متوقف': self.colors['danger'],
            'ملغي': self.colors['dark']
        }
        return colors.get(status, self.colors['dark'])

    def create_activity_panel(self, parent):
        """إنشاء لوحة الأنشطة"""
        # العنوان
        header_frame = tk.Frame(parent, bg=self.colors['accent'], height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="📊 الأنشطة الحديثة",
                              font=self.fonts['subheading'], bg=self.colors['accent'],
                              fg=self.colors['white'])
        title_label.pack(pady=12, padx=20)

        # قائمة الأنشطة
        activities_frame = tk.Frame(parent, bg=self.colors['white'])
        activities_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # أنشطة تجريبية
        activities = [
            ("تم إنشاء مشروع جديد", "منذ ساعتين", "🆕"),
            ("تم تحديث مهمة", "منذ 4 ساعات", "✏️"),
            ("تم رفع ملف جديد", "أمس", "📁"),
            ("تم إنجاز مهمة", "أمس", "✅"),
            ("تم إضافة تعليق", "منذ يومين", "💬")
        ]

        for i, (activity, time, icon) in enumerate(activities):
            activity_frame = tk.Frame(activities_frame, bg=self.colors['light'] if i % 2 == 0 else self.colors['white'])
            activity_frame.pack(fill=tk.X, pady=2)

            icon_label = tk.Label(activity_frame, text=icon, font=('Arial', 16),
                                 bg=activity_frame.cget('bg'))
            icon_label.pack(side=tk.LEFT, padx=(10, 15), pady=10)

            text_frame = tk.Frame(activity_frame, bg=activity_frame.cget('bg'))
            text_frame.pack(side=tk.LEFT, fill=tk.X, expand=True, pady=10)

            activity_label = tk.Label(text_frame, text=activity,
                                     font=self.fonts['small'], bg=activity_frame.cget('bg'),
                                     fg=self.colors['dark'])
            activity_label.pack(anchor=tk.W)

            time_label = tk.Label(text_frame, text=time,
                                 font=self.fonts['small'], bg=activity_frame.cget('bg'),
                                 fg=self.colors['dark'])
            time_label.pack(anchor=tk.W)

    def show_projects(self):
        """عرض إدارة المشاريع"""
        try:
            from advanced_projects import AdvancedProjectsManager

            # إنشاء مدير المشاريع المتقدم
            projects_manager = AdvancedProjectsManager(
                self.main_window, self, self.current_user, self.colors, self.fonts
            )

            # عرض واجهة إدارة المشاريع
            projects_manager.create_projects_interface(self.content_area)

        except ImportError:
            # في حالة عدم توفر الوحدة المتقدمة، عرض النسخة المبسطة
            self.show_simple_projects()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل وحدة إدارة المشاريع:\n{str(e)}")
            self.show_simple_projects()

    def show_simple_projects(self):
        """عرض إدارة المشاريع المبسطة"""
        # مسح المحتوى السابق
        if self.current_content:
            self.current_content.destroy()

        self.current_content = tk.Frame(self.content_area, bg=self.colors['light'])
        self.current_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        header_frame = tk.Frame(self.current_content, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="🏗️ إدارة المشاريع",
                              font=self.fonts['title'], bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)

        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)

        new_btn = tk.Button(tools_frame, text="➕ مشروع جديد",
                           font=self.fonts['button'], bg=self.colors['success'],
                           fg=self.colors['white'], relief=tk.FLAT,
                           command=self.add_simple_project, cursor="hand2")
        new_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)

        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث",
                               font=self.fonts['button'], bg=self.colors['secondary'],
                               fg=self.colors['white'], relief=tk.FLAT,
                               command=self.refresh_simple_projects, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)

        # جدول المشاريع
        table_frame = tk.Frame(self.current_content, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("ID", "اسم المشروع", "النوع", "الموقع", "الحالة", "الأولوية", "نسبة الإنجاز", "الميزانية")
        self.simple_projects_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.simple_projects_tree.heading(col, text=col)
            self.simple_projects_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.simple_projects_tree.yview)
        self.simple_projects_tree.configure(yscrollcommand=scrollbar.set)

        self.simple_projects_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20)

        # تحميل البيانات
        self.refresh_simple_projects()

        # ربط الأحداث
        self.simple_projects_tree.bind('<Double-1>', self.edit_simple_project)

    def refresh_simple_projects(self):
        """تحديث قائمة المشاريع المبسطة"""
        try:
            # مسح البيانات الحالية
            for item in self.simple_projects_tree.get_children():
                self.simple_projects_tree.delete(item)

            # جلب المشاريع
            self.cursor.execute('''
                SELECT id, name, project_type, location, status, priority,
                       completion_percentage, planned_budget
                FROM projects
                ORDER BY created_date DESC
            ''')

            projects = self.cursor.fetchall()

            for project in projects:
                self.simple_projects_tree.insert("", tk.END, values=(
                    project[0],
                    project[1] or "",
                    project[2] or "",
                    project[3] or "",
                    project[4] or "مخطط",
                    project[5] or "متوسط",
                    f"{project[6]:.1f}%" if project[6] else "0%",
                    f"{project[7]:,.0f}" if project[7] else "0"
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المشاريع:\n{str(e)}")

    def add_simple_project(self):
        """إضافة مشروع جديد مبسط"""
        self.simple_project_form()

    def edit_simple_project(self, event=None):
        """تعديل مشروع مبسط"""
        selected = self.simple_projects_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مشروع للتعديل")
            return

        project_id = self.simple_projects_tree.item(selected[0])['values'][0]
        self.simple_project_form(project_id)

    def simple_project_form(self, project_id=None):
        """نموذج المشروع المبسط"""
        is_edit = project_id is not None

        # إنشاء النافذة
        form_window = tk.Toplevel(self.main_window)
        title = "تعديل مشروع" if is_edit else "إضافة مشروع جديد"
        form_window.title(title)
        form_window.geometry("600x500")
        form_window.resizable(False, False)
        form_window.configure(bg=self.colors['light'])
        form_window.transient(self.main_window)
        form_window.grab_set()

        # توسيط النافذة
        self.center_window(form_window, 600, 500)

        # الإطار الرئيسي
        main_frame = tk.Frame(form_window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text=f"🏗️ {title}",
                              font=self.fonts['heading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(pady=15)

        # النموذج
        form_frame = tk.Frame(main_frame, bg=self.colors['white'])
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # متغيرات النموذج
        name_var = tk.StringVar()
        type_var = tk.StringVar()
        location_var = tk.StringVar()
        budget_var = tk.StringVar()
        status_var = tk.StringVar(value="مخطط")
        priority_var = tk.StringVar(value="متوسط")
        completion_var = tk.StringVar(value="0")

        # الحقول
        fields = [
            ("اسم المشروع *:", name_var, "entry"),
            ("نوع المشروع:", type_var, "combo", ["بنية تحتية", "كهرباء", "مياه وصرف صحي", "طرق ومواصلات", "بيئة", "مباني عامة", "أخرى"]),
            ("الموقع:", location_var, "entry"),
            ("الميزانية (ريال):", budget_var, "entry"),
            ("الحالة:", status_var, "combo", ["مخطط", "قيد التنفيذ", "متوقف", "مكتمل", "ملغي"]),
            ("الأولوية:", priority_var, "combo", ["منخفض", "متوسط", "عالي", "حرج"]),
            ("نسبة الإنجاز (%):", completion_var, "scale")
        ]

        widgets = {}
        for i, field in enumerate(fields):
            field_frame = tk.Frame(form_frame, bg=self.colors['white'])
            field_frame.pack(fill=tk.X, padx=20, pady=10)

            label = tk.Label(field_frame, text=field[0], font=self.fonts['body'],
                           bg=self.colors['white'], fg=self.colors['dark'])
            label.pack(anchor=tk.W, pady=(0, 5))

            if field[2] == "entry":
                widget = tk.Entry(field_frame, textvariable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], width=40)
                widget.pack(anchor=tk.W, ipady=5)
            elif field[2] == "combo":
                widget = ttk.Combobox(field_frame, textvariable=field[1], values=field[3],
                                    font=self.fonts['body'], width=37, state="readonly")
                widget.pack(anchor=tk.W, ipady=5)
            elif field[2] == "scale":
                widget = tk.Scale(field_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                                variable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], length=300)
                widget.pack(anchor=tk.W)

            widgets[field[0]] = widget

        # تحميل البيانات في حالة التعديل
        if is_edit:
            try:
                self.cursor.execute('''
                    SELECT name, project_type, location, planned_budget, status,
                           priority, completion_percentage
                    FROM projects WHERE id = ?
                ''', (project_id,))

                project = self.cursor.fetchone()
                if project:
                    name_var.set(project[0] or "")
                    type_var.set(project[1] or "")
                    location_var.set(project[2] or "")
                    budget_var.set(str(project[3] or ""))
                    status_var.set(project[4] or "مخطط")
                    priority_var.set(project[5] or "متوسط")
                    completion_var.set(str(project[6] or "0"))
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل بيانات المشروع:\n{str(e)}")

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(main_frame, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X)

        def save_project():
            name = name_var.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المشروع")
                return

            try:
                project_type = type_var.get()
                location = location_var.get().strip()
                budget = float(budget_var.get()) if budget_var.get() else 0
                status = status_var.get()
                priority = priority_var.get()
                completion = float(completion_var.get())

                if is_edit:
                    self.cursor.execute('''
                        UPDATE projects SET
                            name = ?, project_type = ?, location = ?, planned_budget = ?,
                            status = ?, priority = ?, completion_percentage = ?,
                            last_modified = ?
                        WHERE id = ?
                    ''', (name, project_type, location, budget, status, priority,
                          completion, datetime.now(), project_id))
                    message = "تم تحديث المشروع بنجاح"
                else:
                    self.cursor.execute('''
                        INSERT INTO projects (
                            name, project_type, location, planned_budget, status,
                            priority, completion_percentage, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (name, project_type, location, budget, status, priority,
                          completion, self.current_user['id']))
                    message = "تم إضافة المشروع بنجاح"

                self.conn.commit()
                messagebox.showinfo("نجح", message)
                form_window.destroy()
                self.refresh_simple_projects()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى التحقق من صحة البيانات المالية")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المشروع:\n{str(e)}")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ",
                           font=self.fonts['button'], bg=self.colors['success'],
                           fg=self.colors['white'], relief=tk.FLAT,
                           command=save_project, cursor="hand2")
        save_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                             font=self.fonts['button'], bg=self.colors['danger'],
                             fg=self.colors['white'], relief=tk.FLAT,
                             command=form_window.destroy, cursor="hand2")
        cancel_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

    def show_tasks(self):
        """عرض إدارة المهام"""
        try:
            from advanced_tasks import AdvancedTasksManager

            # إنشاء مدير المهام المتقدم
            tasks_manager = AdvancedTasksManager(
                self.main_window, self, self.current_user, self.colors, self.fonts
            )

            # عرض واجهة إدارة المهام
            tasks_manager.create_tasks_interface(self.content_area)

        except ImportError:
            # في حالة عدم توفر الوحدة المتقدمة، عرض النسخة المبسطة
            self.show_simple_tasks()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل وحدة إدارة المهام:\n{str(e)}")
            self.show_simple_tasks()

    def show_simple_tasks(self):
        """عرض إدارة المهام المبسطة"""
        # مسح المحتوى السابق
        if self.current_content:
            self.current_content.destroy()

        self.current_content = tk.Frame(self.content_area, bg=self.colors['light'])
        self.current_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        header_frame = tk.Frame(self.current_content, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="📋 إدارة المهام",
                              font=self.fonts['title'], bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)

        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)

        new_btn = tk.Button(tools_frame, text="➕ مهمة جديدة",
                           font=self.fonts['button'], bg=self.colors['success'],
                           fg=self.colors['white'], relief=tk.FLAT,
                           command=self.add_simple_task, cursor="hand2")
        new_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)

        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث",
                               font=self.fonts['button'], bg=self.colors['secondary'],
                               fg=self.colors['white'], relief=tk.FLAT,
                               command=self.refresh_simple_tasks, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)

        # جدول المهام
        table_frame = tk.Frame(self.current_content, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("ID", "اسم المهمة", "المشروع", "المسؤول", "الحالة", "الأولوية", "نسبة الإنجاز", "تاريخ النهاية")
        self.simple_tasks_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.simple_tasks_tree.heading(col, text=col)
            self.simple_tasks_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.simple_tasks_tree.yview)
        self.simple_tasks_tree.configure(yscrollcommand=scrollbar.set)

        self.simple_tasks_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20)

        # تحميل البيانات
        self.refresh_simple_tasks()

        # ربط الأحداث
        self.simple_tasks_tree.bind('<Double-1>', self.edit_simple_task)

    def refresh_simple_tasks(self):
        """تحديث قائمة المهام المبسطة"""
        try:
            # مسح البيانات الحالية
            for item in self.simple_tasks_tree.get_children():
                self.simple_tasks_tree.delete(item)

            # جلب المهام
            self.cursor.execute('''
                SELECT t.id, t.name, p.name as project_name, u.full_name as assigned_name,
                       t.status, t.priority, t.completion_percentage, t.end_date
                FROM tasks t
                LEFT JOIN projects p ON t.project_id = p.id
                LEFT JOIN users u ON t.assigned_to = u.id
                ORDER BY t.created_date DESC
            ''')

            tasks = self.cursor.fetchall()

            for task in tasks:
                self.simple_tasks_tree.insert("", tk.END, values=(
                    task[0],
                    task[1] or "",
                    task[2] or "غير محدد",
                    task[3] or "غير محدد",
                    task[4] or "غير مبدوء",
                    task[5] or "متوسط",
                    f"{task[6]:.1f}%" if task[6] else "0%",
                    task[7] or ""
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المهام:\n{str(e)}")

    def add_simple_task(self):
        """إضافة مهمة جديدة مبسطة"""
        self.simple_task_form()

    def edit_simple_task(self, event=None):
        """تعديل مهمة مبسطة"""
        selected = self.simple_tasks_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مهمة للتعديل")
            return

        task_id = self.simple_tasks_tree.item(selected[0])['values'][0]
        self.simple_task_form(task_id)

    def simple_task_form(self, task_id=None):
        """نموذج المهمة المبسط"""
        is_edit = task_id is not None

        # إنشاء النافذة
        form_window = tk.Toplevel(self.main_window)
        title = "تعديل مهمة" if is_edit else "إضافة مهمة جديدة"
        form_window.title(title)
        form_window.geometry("600x600")
        form_window.resizable(False, False)
        form_window.configure(bg=self.colors['light'])
        form_window.transient(self.main_window)
        form_window.grab_set()

        # توسيط النافذة
        self.center_window(form_window, 600, 600)

        # الإطار الرئيسي
        main_frame = tk.Frame(form_window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text=f"📋 {title}",
                              font=self.fonts['heading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(pady=15)

        # النموذج
        form_frame = tk.Frame(main_frame, bg=self.colors['white'])
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # متغيرات النموذج
        name_var = tk.StringVar()
        project_var = tk.StringVar()
        assigned_var = tk.StringVar()
        description_var = tk.StringVar()
        start_date_var = tk.StringVar()
        end_date_var = tk.StringVar()
        estimated_hours_var = tk.StringVar()
        status_var = tk.StringVar(value="غير مبدوء")
        priority_var = tk.StringVar(value="متوسط")
        completion_var = tk.StringVar(value="0")

        # جلب قوائم البيانات
        projects_list = self.get_projects_for_tasks()
        users_list = self.get_users_for_tasks()

        # الحقول
        fields = [
            ("اسم المهمة *:", name_var, "entry"),
            ("المشروع:", project_var, "combo", projects_list),
            ("المسؤول:", assigned_var, "combo", users_list),
            ("الوصف:", description_var, "text"),
            ("تاريخ البداية:", start_date_var, "entry"),
            ("تاريخ النهاية:", end_date_var, "entry"),
            ("الساعات المقدرة:", estimated_hours_var, "entry"),
            ("الحالة:", status_var, "combo", ["غير مبدوء", "قيد التنفيذ", "مكتمل", "متوقف", "ملغي"]),
            ("الأولوية:", priority_var, "combo", ["منخفض", "متوسط", "عالي", "حرج"]),
            ("نسبة الإنجاز (%):", completion_var, "scale")
        ]

        widgets = {}
        for i, field in enumerate(fields):
            field_frame = tk.Frame(form_frame, bg=self.colors['white'])
            field_frame.pack(fill=tk.X, padx=20, pady=8)

            label = tk.Label(field_frame, text=field[0], font=self.fonts['body'],
                           bg=self.colors['white'], fg=self.colors['dark'])
            label.pack(anchor=tk.W, pady=(0, 3))

            if field[2] == "entry":
                widget = tk.Entry(field_frame, textvariable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], width=40)
                widget.pack(anchor=tk.W, ipady=3)
            elif field[2] == "combo":
                widget = ttk.Combobox(field_frame, textvariable=field[1], values=field[3],
                                    font=self.fonts['body'], width=37, state="readonly")
                widget.pack(anchor=tk.W, ipady=3)
            elif field[2] == "text":
                widget = tk.Text(field_frame, height=3, font=self.fonts['body'],
                               bg=self.colors['light'], width=40)
                widget.pack(anchor=tk.W)
            elif field[2] == "scale":
                widget = tk.Scale(field_frame, from_=0, to=100, orient=tk.HORIZONTAL,
                                variable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], length=300)
                widget.pack(anchor=tk.W)

            widgets[field[0]] = widget

        # تحميل البيانات في حالة التعديل
        if is_edit:
            try:
                self.cursor.execute('''
                    SELECT t.name, p.name as project_name, u.full_name as assigned_name,
                           t.description, t.start_date, t.end_date, t.estimated_hours,
                           t.status, t.priority, t.completion_percentage
                    FROM tasks t
                    LEFT JOIN projects p ON t.project_id = p.id
                    LEFT JOIN users u ON t.assigned_to = u.id
                    WHERE t.id = ?
                ''', (task_id,))

                task = self.cursor.fetchone()
                if task:
                    name_var.set(task[0] or "")
                    project_var.set(task[1] or "")
                    assigned_var.set(task[2] or "")
                    if task[3]:
                        widgets["الوصف:"].insert("1.0", task[3])
                    start_date_var.set(task[4] or "")
                    end_date_var.set(task[5] or "")
                    estimated_hours_var.set(str(task[6] or ""))
                    status_var.set(task[7] or "غير مبدوء")
                    priority_var.set(task[8] or "متوسط")
                    completion_var.set(str(task[9] or "0"))
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل بيانات المهمة:\n{str(e)}")

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(main_frame, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X)

        def save_task():
            name = name_var.get().strip()
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المهمة")
                return

            try:
                # جمع البيانات
                project_name = project_var.get()
                assigned_name = assigned_var.get()
                description = widgets["الوصف:"].get("1.0", tk.END).strip()
                start_date = start_date_var.get() or None
                end_date = end_date_var.get() or None
                estimated_hours = float(estimated_hours_var.get()) if estimated_hours_var.get() else 0
                status = status_var.get()
                priority = priority_var.get()
                completion = float(completion_var.get())

                # الحصول على معرفات المشروع والمستخدم
                project_id = None
                if project_name:
                    project = self.cursor.execute('SELECT id FROM projects WHERE name = ?', (project_name,)).fetchone()
                    if project:
                        project_id = project[0]

                assigned_id = None
                if assigned_name:
                    user = self.cursor.execute('SELECT id FROM users WHERE full_name = ?', (assigned_name,)).fetchone()
                    if user:
                        assigned_id = user[0]

                if is_edit:
                    self.cursor.execute('''
                        UPDATE tasks SET
                            name = ?, project_id = ?, assigned_to = ?, description = ?,
                            start_date = ?, end_date = ?, estimated_hours = ?,
                            status = ?, priority = ?, completion_percentage = ?,
                            last_modified = ?
                        WHERE id = ?
                    ''', (name, project_id, assigned_id, description, start_date, end_date,
                          estimated_hours, status, priority, completion, datetime.now(), task_id))
                    message = "تم تحديث المهمة بنجاح"
                else:
                    self.cursor.execute('''
                        INSERT INTO tasks (
                            name, project_id, assigned_to, description, start_date, end_date,
                            estimated_hours, status, priority, completion_percentage, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (name, project_id, assigned_id, description, start_date, end_date,
                          estimated_hours, status, priority, completion, self.current_user['id']))
                    message = "تم إضافة المهمة بنجاح"

                self.conn.commit()
                messagebox.showinfo("نجح", message)
                form_window.destroy()
                self.refresh_simple_tasks()

            except ValueError:
                messagebox.showerror("خطأ", "يرجى التحقق من صحة البيانات الرقمية")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المهمة:\n{str(e)}")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ",
                           font=self.fonts['button'], bg=self.colors['success'],
                           fg=self.colors['white'], relief=tk.FLAT,
                           command=save_task, cursor="hand2")
        save_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                             font=self.fonts['button'], bg=self.colors['danger'],
                             fg=self.colors['white'], relief=tk.FLAT,
                             command=form_window.destroy, cursor="hand2")
        cancel_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

    def get_projects_for_tasks(self):
        """جلب قائمة المشاريع للمهام"""
        try:
            projects = self.cursor.execute('SELECT name FROM projects ORDER BY name').fetchall()
            return [""] + [project[0] for project in projects]
        except:
            return [""]

    def get_users_for_tasks(self):
        """جلب قائمة المستخدمين للمهام"""
        try:
            users = self.cursor.execute('SELECT full_name FROM users WHERE is_active = 1 ORDER BY full_name').fetchall()
            return [""] + [user[0] for user in users]
        except:
            return [""]

    def show_users(self):
        """عرض إدارة المستخدمين"""
        try:
            from advanced_users import AdvancedUsersManager

            # إنشاء مدير المستخدمين المتقدم
            users_manager = AdvancedUsersManager(
                self.main_window, self, self.current_user, self.colors, self.fonts
            )

            # عرض واجهة إدارة المستخدمين
            users_manager.create_users_interface(self.content_area)

        except ImportError:
            # في حالة عدم توفر الوحدة المتقدمة، عرض النسخة المبسطة
            self.show_simple_users()
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل وحدة إدارة المستخدمين:\n{str(e)}")
            self.show_simple_users()

    def show_simple_users(self):
        """عرض إدارة المستخدمين المبسطة"""
        # مسح المحتوى السابق
        if self.current_content:
            self.current_content.destroy()

        self.current_content = tk.Frame(self.content_area, bg=self.colors['light'])
        self.current_content.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        header_frame = tk.Frame(self.current_content, bg=self.colors['white'], height=80)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="👥 إدارة المستخدمين",
                              font=self.fonts['title'], bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(side=tk.LEFT, pady=20, padx=20)

        # أزرار الأدوات
        tools_frame = tk.Frame(header_frame, bg=self.colors['white'])
        tools_frame.pack(side=tk.RIGHT, pady=20, padx=20)

        new_btn = tk.Button(tools_frame, text="➕ مستخدم جديد",
                           font=self.fonts['button'], bg=self.colors['success'],
                           fg=self.colors['white'], relief=tk.FLAT,
                           command=self.add_simple_user, cursor="hand2")
        new_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)

        refresh_btn = tk.Button(tools_frame, text="🔄 تحديث",
                               font=self.fonts['button'], bg=self.colors['secondary'],
                               fg=self.colors['white'], relief=tk.FLAT,
                               command=self.refresh_simple_users, cursor="hand2")
        refresh_btn.pack(side=tk.RIGHT, padx=5, ipadx=15, ipady=8)

        # جدول المستخدمين
        table_frame = tk.Frame(self.current_content, bg=self.colors['white'])
        table_frame.pack(fill=tk.BOTH, expand=True)

        columns = ("ID", "اسم المستخدم", "الاسم الكامل", "البريد الإلكتروني", "الدور", "القسم", "الحالة", "آخر دخول")
        self.simple_users_tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=15)

        for col in columns:
            self.simple_users_tree.heading(col, text=col)
            self.simple_users_tree.column(col, width=120, anchor=tk.CENTER)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.simple_users_tree.yview)
        self.simple_users_tree.configure(yscrollcommand=scrollbar.set)

        self.simple_users_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=20, pady=20)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y, pady=20)

        # تحميل البيانات
        self.refresh_simple_users()

        # ربط الأحداث
        self.simple_users_tree.bind('<Double-1>', self.edit_simple_user)

    def refresh_simple_users(self):
        """تحديث قائمة المستخدمين المبسطة"""
        try:
            # مسح البيانات الحالية
            for item in self.simple_users_tree.get_children():
                self.simple_users_tree.delete(item)

            # جلب المستخدمين
            self.cursor.execute('''
                SELECT id, username, full_name, email, role, department, is_active, last_login
                FROM users
                ORDER BY created_date DESC
            ''')

            users = self.cursor.fetchall()

            for user in users:
                role_arabic = self.get_role_name(user[4]) if user[4] else ""
                status = "نشط" if user[6] else "غير نشط"
                last_login = user[7][:16] if user[7] else "لم يسجل دخول"

                self.simple_users_tree.insert("", tk.END, values=(
                    user[0],
                    user[1] or "",
                    user[2] or "",
                    user[3] or "",
                    role_arabic,
                    user[5] or "",
                    status,
                    last_login
                ))

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل المستخدمين:\n{str(e)}")

    def add_simple_user(self):
        """إضافة مستخدم جديد مبسط"""
        self.simple_user_form()

    def edit_simple_user(self, event=None):
        """تعديل مستخدم مبسط"""
        selected = self.simple_users_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مستخدم للتعديل")
            return

        user_id = self.simple_users_tree.item(selected[0])['values'][0]
        self.simple_user_form(user_id)

    def simple_user_form(self, user_id=None):
        """نموذج المستخدم المبسط"""
        is_edit = user_id is not None

        # إنشاء النافذة
        form_window = tk.Toplevel(self.main_window)
        title = "تعديل مستخدم" if is_edit else "إضافة مستخدم جديد"
        form_window.title(title)
        form_window.geometry("600x500")
        form_window.resizable(False, False)
        form_window.configure(bg=self.colors['light'])
        form_window.transient(self.main_window)
        form_window.grab_set()

        # توسيط النافذة
        self.center_window(form_window, 600, 500)

        # الإطار الرئيسي
        main_frame = tk.Frame(form_window, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X, pady=(0, 20))
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text=f"👥 {title}",
                              font=self.fonts['heading'], bg=self.colors['primary'],
                              fg=self.colors['white'])
        title_label.pack(pady=15)

        # النموذج
        form_frame = tk.Frame(main_frame, bg=self.colors['white'])
        form_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # متغيرات النموذج
        username_var = tk.StringVar()
        password_var = tk.StringVar()
        full_name_var = tk.StringVar()
        email_var = tk.StringVar()
        phone_var = tk.StringVar()
        role_var = tk.StringVar(value="Employee")
        department_var = tk.StringVar()
        is_active_var = tk.BooleanVar(value=True)

        # الحقول
        fields = [
            ("اسم المستخدم *:", username_var, "entry"),
            ("كلمة المرور *:", password_var, "password"),
            ("الاسم الكامل *:", full_name_var, "entry"),
            ("البريد الإلكتروني:", email_var, "entry"),
            ("رقم الهاتف:", phone_var, "entry"),
            ("الدور:", role_var, "combo", ["Employee", "ProjectManager", "SystemManager", "Admin"]),
            ("القسم:", department_var, "combo", ["الهندسة", "تقنية المعلومات", "الإشراف", "الإدارة", "المالية"]),
            ("الحساب نشط:", is_active_var, "checkbox")
        ]

        widgets = {}
        for i, field in enumerate(fields):
            field_frame = tk.Frame(form_frame, bg=self.colors['white'])
            field_frame.pack(fill=tk.X, padx=20, pady=10)

            label = tk.Label(field_frame, text=field[0], font=self.fonts['body'],
                           bg=self.colors['white'], fg=self.colors['dark'])
            label.pack(anchor=tk.W, pady=(0, 5))

            if field[2] == "entry":
                widget = tk.Entry(field_frame, textvariable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], width=40)
                widget.pack(anchor=tk.W, ipady=5)
            elif field[2] == "password":
                widget = tk.Entry(field_frame, textvariable=field[1], font=self.fonts['body'],
                                bg=self.colors['light'], width=40, show="*")
                widget.pack(anchor=tk.W, ipady=5)
            elif field[2] == "combo":
                widget = ttk.Combobox(field_frame, textvariable=field[1], values=field[3],
                                    font=self.fonts['body'], width=37, state="readonly")
                widget.pack(anchor=tk.W, ipady=5)
            elif field[2] == "checkbox":
                widget = tk.Checkbutton(field_frame, variable=field[1], font=self.fonts['body'],
                                      bg=self.colors['white'], text="تفعيل الحساب")
                widget.pack(anchor=tk.W)

            widgets[field[0]] = widget

        # إخفاء حقل كلمة المرور في وضع التعديل
        if is_edit:
            widgets["كلمة المرور *:"].master.pack_forget()

        # تحميل البيانات في حالة التعديل
        if is_edit:
            try:
                self.cursor.execute('''
                    SELECT username, full_name, email, phone, role, department, is_active
                    FROM users WHERE id = ?
                ''', (user_id,))

                user = self.cursor.fetchone()
                if user:
                    username_var.set(user[0] or "")
                    full_name_var.set(user[1] or "")
                    email_var.set(user[2] or "")
                    phone_var.set(user[3] or "")
                    role_var.set(user[4] or "Employee")
                    department_var.set(user[5] or "")
                    is_active_var.set(bool(user[6]))
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في تحميل بيانات المستخدم:\n{str(e)}")

        # أزرار الحفظ والإلغاء
        buttons_frame = tk.Frame(main_frame, bg=self.colors['light'])
        buttons_frame.pack(fill=tk.X)

        def save_user():
            username = username_var.get().strip()
            full_name = full_name_var.get().strip()

            if not username:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
                return

            if not full_name:
                messagebox.showerror("خطأ", "يرجى إدخال الاسم الكامل")
                return

            if not is_edit:
                password = password_var.get()
                if not password:
                    messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                    return

            try:
                email = email_var.get().strip()
                phone = phone_var.get().strip()
                role = role_var.get()
                department = department_var.get()
                is_active = is_active_var.get()

                if is_edit:
                    self.cursor.execute('''
                        UPDATE users SET
                            username = ?, full_name = ?, email = ?, phone = ?,
                            role = ?, department = ?, is_active = ?
                        WHERE id = ?
                    ''', (username, full_name, email, phone, role, department,
                          is_active, user_id))
                    message = "تم تحديث المستخدم بنجاح"
                else:
                    # التحقق من عدم وجود اسم المستخدم
                    existing = self.cursor.execute(
                        "SELECT id FROM users WHERE username = ?", (username,)
                    ).fetchone()

                    if existing:
                        messagebox.showerror("خطأ", "اسم المستخدم موجود بالفعل")
                        return

                    # تشفير كلمة المرور
                    hashed_password = hashlib.sha256(password.encode()).hexdigest()

                    self.cursor.execute('''
                        INSERT INTO users (
                            username, password, full_name, email, phone, role,
                            department, is_active, created_by
                        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (username, hashed_password, full_name, email, phone, role,
                          department, is_active, self.current_user['id']))
                    message = "تم إضافة المستخدم بنجاح"

                self.conn.commit()
                messagebox.showinfo("نجح", message)
                form_window.destroy()
                self.refresh_simple_users()

            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حفظ المستخدم:\n{str(e)}")

        save_btn = tk.Button(buttons_frame, text="💾 حفظ",
                           font=self.fonts['button'], bg=self.colors['success'],
                           fg=self.colors['white'], relief=tk.FLAT,
                           command=save_user, cursor="hand2")
        save_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                             font=self.fonts['button'], bg=self.colors['danger'],
                             fg=self.colors['white'], relief=tk.FLAT,
                             command=form_window.destroy, cursor="hand2")
        cancel_btn.pack(side=tk.RIGHT, padx=5, ipadx=20, ipady=10)

    def show_reports(self):
        """عرض التقارير"""
        messagebox.showinfo("قريباً", "وحدة التقارير المتقدمة قيد التطوير")

    def show_files(self):
        """عرض إدارة الملفات"""
        messagebox.showinfo("قريباً", "وحدة إدارة الملفات قيد التطوير")

    def show_comments(self):
        """عرض التعليقات"""
        messagebox.showinfo("قريباً", "وحدة التعليقات قيد التطوير")

    def show_settings(self):
        """عرض الإعدادات"""
        messagebox.showinfo("قريباً", "وحدة الإعدادات قيد التطوير")

    def __del__(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        if hasattr(self, 'conn'):
            self.conn.close()

def main():
    """الدالة الرئيسية"""
    try:
        app = ModernSimpleProjectManager()
        app.run()
    except Exception as e:
        messagebox.showerror("خطأ", f"حدث خطأ في التطبيق:\n{str(e)}")

if __name__ == "__main__":
    main()
