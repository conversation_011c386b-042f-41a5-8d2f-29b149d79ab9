#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نموذج إضافة وتعديل المستخدمين
User Add/Edit Form
"""

import tkinter as tk
from tkinter import ttk, messagebox
import customtkinter as ctk
from datetime import datetime

class UserForm:
    """فئة نموذج المستخدم"""
    
    def __init__(self, parent_window, db_manager, user_id=None):
        """تهيئة النموذج"""
        self.parent_window = parent_window
        self.db_manager = db_manager
        self.user_id = user_id
        self.window = None
        self.is_edit_mode = user_id is not None
        
        # متغيرات النموذج
        self.username_var = tk.StringVar()
        self.password_var = tk.StringVar()
        self.confirm_password_var = tk.StringVar()
        self.employee_var = tk.StringVar()
        self.role_var = tk.StringVar()
        self.is_active_var = tk.BooleanVar(value=True)
        
        self.employees_data = []
        
    def show(self):
        """عرض النموذج"""
        self.window = ctk.CTkToplevel(self.parent_window)
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        self.window.title(title)
        self.window.geometry("500x600")
        self.window.transient(self.parent_window)
        self.window.grab_set()
        
        self.setup_ui()
        self.load_employees()
        
        if self.is_edit_mode:
            self.load_user_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان
        title = "تعديل مستخدم" if self.is_edit_mode else "إضافة مستخدم جديد"
        title_label = ctk.CTkLabel(
            self.window,
            text=title,
            font=ctk.CTkFont(size=20, weight="bold")
        )
        title_label.pack(pady=20)
        
        # الإطار الرئيسي
        main_frame = ctk.CTkFrame(self.window)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # اسم المستخدم
        username_label = ctk.CTkLabel(main_frame, text="اسم المستخدم *:")
        username_label.pack(anchor="w", padx=20, pady=(20, 5))
        
        self.username_entry = ctk.CTkEntry(
            main_frame,
            textvariable=self.username_var,
            placeholder_text="أدخل اسم المستخدم",
            height=35
        )
        self.username_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # كلمة المرور
        password_label = ctk.CTkLabel(main_frame, text="كلمة المرور *:")
        password_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.password_entry = ctk.CTkEntry(
            main_frame,
            textvariable=self.password_var,
            placeholder_text="أدخل كلمة المرور",
            show="*",
            height=35
        )
        self.password_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # تأكيد كلمة المرور
        confirm_password_label = ctk.CTkLabel(main_frame, text="تأكيد كلمة المرور *:")
        confirm_password_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.confirm_password_entry = ctk.CTkEntry(
            main_frame,
            textvariable=self.confirm_password_var,
            placeholder_text="أعد إدخال كلمة المرور",
            show="*",
            height=35
        )
        self.confirm_password_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # الموظف المرتبط
        employee_label = ctk.CTkLabel(main_frame, text="الموظف المرتبط:")
        employee_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        self.employee_combo = ctk.CTkComboBox(
            main_frame,
            variable=self.employee_var,
            values=["اختر موظف..."],
            height=35,
            state="readonly"
        )
        self.employee_combo.pack(fill="x", padx=20, pady=(0, 15))
        
        # الصلاحية
        role_label = ctk.CTkLabel(main_frame, text="الصلاحية *:")
        role_label.pack(anchor="w", padx=20, pady=(0, 5))
        
        roles = ["Admin", "ProjectManager", "Employee", "SystemManager"]
        role_names = ["مدير النظام", "مدير مشروع", "موظف", "مدير النظام"]
        
        self.role_combo = ctk.CTkComboBox(
            main_frame,
            variable=self.role_var,
            values=roles,
            height=35,
            state="readonly"
        )
        self.role_combo.pack(fill="x", padx=20, pady=(0, 15))
        
        # حالة المستخدم
        self.active_checkbox = ctk.CTkCheckBox(
            main_frame,
            text="المستخدم نشط",
            variable=self.is_active_var
        )
        self.active_checkbox.pack(anchor="w", padx=20, pady=15)
        
        # أزرار التحكم
        buttons_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
        buttons_frame.pack(fill="x", padx=20, pady=20)
        
        save_button = ctk.CTkButton(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_user,
            width=100,
            height=40,
            fg_color="#27ae60",
            hover_color="#229954"
        )
        save_button.pack(side="right", padx=5)
        
        cancel_button = ctk.CTkButton(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel,
            width=100,
            height=40,
            fg_color="#95a5a6",
            hover_color="#7f8c8d"
        )
        cancel_button.pack(side="right", padx=5)
        
        # تركيز على أول حقل
        self.username_entry.focus()
    
    def load_employees(self):
        """تحميل قائمة الموظفين"""
        employees = self.db_manager.get_employees()
        
        if employees:
            self.employees_data = employees
            employee_names = ["بدون موظف مرتبط"] + [f"{emp[1]} - {emp[2]}" for emp in employees]
            self.employee_combo.configure(values=employee_names)
            self.employee_combo.set("بدون موظف مرتبط")
    
    def load_user_data(self):
        """تحميل بيانات المستخدم للتعديل"""
        query = """
            SELECT u.Username, u.EmployeeID, u.UserRole, u.IsActive, e.FullName, e.Position
            FROM Users u
            LEFT JOIN Employees e ON u.EmployeeID = e.EmployeeID
            WHERE u.UserID = ?
        """
        
        result = self.db_manager.execute_query(query, (self.user_id,))
        
        if result and len(result) > 0:
            user_data = result[0]
            
            # تعبئة الحقول
            self.username_var.set(user_data[0])
            self.role_var.set(user_data[2])
            self.is_active_var.set(user_data[3])
            
            # تعيين الموظف المرتبط
            if user_data[1]:  # إذا كان هناك موظف مرتبط
                employee_text = f"{user_data[4]} - {user_data[5]}"
                self.employee_var.set(employee_text)
            else:
                self.employee_var.set("بدون موظف مرتبط")
            
            # إخفاء حقول كلمة المرور في وضع التعديل
            self.password_entry.configure(placeholder_text="اتركه فارغاً للاحتفاظ بكلمة المرور الحالية")
            self.confirm_password_entry.configure(placeholder_text="اتركه فارغاً للاحتفاظ بكلمة المرور الحالية")
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        # التحقق من اسم المستخدم
        if not self.username_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم المستخدم")
            self.username_entry.focus()
            return False
        
        # التحقق من كلمة المرور (للمستخدمين الجدد فقط)
        if not self.is_edit_mode:
            if not self.password_var.get():
                messagebox.showerror("خطأ", "يرجى إدخال كلمة المرور")
                self.password_entry.focus()
                return False
            
            if len(self.password_var.get()) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                self.password_entry.focus()
                return False
            
            if self.password_var.get() != self.confirm_password_var.get():
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقين")
                self.confirm_password_entry.focus()
                return False
        
        # التحقق من كلمة المرور في وضع التعديل (إذا تم إدخالها)
        elif self.is_edit_mode and self.password_var.get():
            if len(self.password_var.get()) < 6:
                messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل")
                self.password_entry.focus()
                return False
            
            if self.password_var.get() != self.confirm_password_var.get():
                messagebox.showerror("خطأ", "كلمة المرور وتأكيدها غير متطابقين")
                self.confirm_password_entry.focus()
                return False
        
        # التحقق من الصلاحية
        if not self.role_var.get():
            messagebox.showerror("خطأ", "يرجى اختيار الصلاحية")
            return False
        
        return True
    
    def save_user(self):
        """حفظ بيانات المستخدم"""
        if not self.validate_form():
            return
        
        try:
            # تحديد معرف الموظف المرتبط
            employee_id = None
            if self.employee_var.get() != "بدون موظف مرتبط":
                # البحث عن معرف الموظف
                for emp in self.employees_data:
                    if f"{emp[1]} - {emp[2]}" == self.employee_var.get():
                        employee_id = emp[0]
                        break
            
            if self.is_edit_mode:
                # تحديث المستخدم
                if self.password_var.get():
                    # تحديث مع كلمة مرور جديدة
                    hashed_password = self.db_manager.hash_password(self.password_var.get())
                    query = """
                        UPDATE Users 
                        SET Username = ?, Password = ?, EmployeeID = ?, UserRole = ?, IsActive = ?
                        WHERE UserID = ?
                    """
                    params = (
                        self.username_var.get().strip(),
                        hashed_password,
                        employee_id,
                        self.role_var.get(),
                        self.is_active_var.get(),
                        self.user_id
                    )
                else:
                    # تحديث بدون تغيير كلمة المرور
                    query = """
                        UPDATE Users 
                        SET Username = ?, EmployeeID = ?, UserRole = ?, IsActive = ?
                        WHERE UserID = ?
                    """
                    params = (
                        self.username_var.get().strip(),
                        employee_id,
                        self.role_var.get(),
                        self.is_active_var.get(),
                        self.user_id
                    )
            else:
                # إضافة مستخدم جديد
                hashed_password = self.db_manager.hash_password(self.password_var.get())
                query = """
                    INSERT INTO Users (Username, Password, EmployeeID, UserRole, IsActive, CreatedDate)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                params = (
                    self.username_var.get().strip(),
                    hashed_password,
                    employee_id,
                    self.role_var.get(),
                    self.is_active_var.get(),
                    datetime.now()
                )
            
            # تنفيذ الاستعلام
            if self.db_manager.execute_non_query(query, params):
                action = "تحديث" if self.is_edit_mode else "إضافة"
                messagebox.showinfo("نجح", f"تم {action} المستخدم بنجاح")
                self.window.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في حفظ بيانات المستخدم")
        
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
    
    def cancel(self):
        """إلغاء العملية"""
        self.window.destroy()
